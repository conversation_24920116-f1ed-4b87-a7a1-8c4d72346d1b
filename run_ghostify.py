#!/usr/bin/env python3
"""
Ghostify Me Launcher Script
Easy startup script for the Ghostify Me application
"""

import os
import sys
import subprocess
import webbrowser
import time
from pathlib import Path

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 9):
        print("❌ Python 3.9 or higher is required")
        print(f"   Current version: {sys.version}")
        return False
    return True

def check_environment():
    """Check if environment is properly set up"""
    print("🔍 Checking environment...")
    
    # Check if .env file exists
    if not os.path.exists('.env'):
        print("⚠️ .env file not found")
        if os.path.exists('.env.example'):
            print("📄 Creating .env from template...")
            import shutil
            shutil.copy('.env.example', '.env')
            print("✅ .env file created")
            print("⚠️ Please edit .env file with your API keys before running")
            return False
        else:
            print("❌ .env.example not found")
            return False
    
    # Check if <PERSON> is available
    if not os.path.exists('sherlock'):
        print("⚠️ Sherlock not found - some features will be limited")
        print("   Run setup.py to download <PERSON> automatically")
    
    return True

def install_dependencies():
    """Install required dependencies"""
    print("📦 Installing dependencies...")
    
    try:
        subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ], check=True, capture_output=True)
        print("✅ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False

def start_streamlit():
    """Start the Streamlit application"""
    print("🚀 Starting Ghostify Me...")
    
    # Set environment variables
    env = os.environ.copy()
    env['STREAMLIT_SERVER_PORT'] = '8501'
    env['STREAMLIT_SERVER_ADDRESS'] = '0.0.0.0'
    
    try:
        # Start Streamlit
        process = subprocess.Popen([
            sys.executable, "-m", "streamlit", "run", "ghostify.py",
            "--server.port=8501",
            "--server.address=0.0.0.0",
            "--server.headless=true"
        ], env=env)
        
        print("⏳ Starting application...")
        time.sleep(3)  # Give Streamlit time to start
        
        # Open browser
        url = "http://localhost:8501"
        print(f"🌐 Opening browser at {url}")
        webbrowser.open(url)
        
        print("✅ Ghostify Me is running!")
        print("📋 Application Info:")
        print(f"   URL: {url}")
        print(f"   Process ID: {process.pid}")
        print("\n🛑 Press Ctrl+C to stop the application")
        
        # Wait for the process
        try:
            process.wait()
        except KeyboardInterrupt:
            print("\n🛑 Stopping Ghostify Me...")
            process.terminate()
            process.wait()
            print("✅ Application stopped")
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to start application: {e}")
        return False

def run_tests():
    """Run the test suite"""
    print("🧪 Running tests...")
    
    try:
        result = subprocess.run([sys.executable, "test_ghostify.py"], 
                              capture_output=True, text=True)
        print(result.stdout)
        if result.stderr:
            print("Errors:", result.stderr)
        return result.returncode == 0
    except Exception as e:
        print(f"❌ Failed to run tests: {e}")
        return False

def show_help():
    """Show help information"""
    print("""
👻 Ghostify Me - Launcher Help

Usage: python run_ghostify.py [option]

Options:
  start     Start the Ghostify Me application (default)
  test      Run the test suite
  setup     Run the setup script
  help      Show this help message

Examples:
  python run_ghostify.py          # Start the application
  python run_ghostify.py start    # Start the application
  python run_ghostify.py test     # Run tests
  python run_ghostify.py setup    # Run setup

Requirements:
  - Python 3.9 or higher
  - Internet connection
  - OpenAI API key (for AI analysis)

For more information, see README.md
""")

def main():
    """Main launcher function"""
    print("👻 Ghostify Me - Digital Footprint Scanner")
    print("=" * 50)
    
    # Parse command line arguments
    command = sys.argv[1] if len(sys.argv) > 1 else "start"
    
    if command == "help":
        show_help()
        return
    
    if command == "test":
        if not check_python_version():
            return
        run_tests()
        return
    
    if command == "setup":
        if not check_python_version():
            return
        try:
            subprocess.run([sys.executable, "setup.py"], check=True)
        except subprocess.CalledProcessError:
            print("❌ Setup failed")
        return
    
    if command == "start":
        # Check Python version
        if not check_python_version():
            return
        
        # Check environment
        if not check_environment():
            print("\n💡 Tip: Run 'python run_ghostify.py setup' to set up automatically")
            return
        
        # Try to install dependencies if needed
        try:
            import streamlit
        except ImportError:
            print("📦 Streamlit not found, installing dependencies...")
            if not install_dependencies():
                return
        
        # Start the application
        start_streamlit()
    else:
        print(f"❌ Unknown command: {command}")
        print("   Use 'python run_ghostify.py help' for usage information")

if __name__ == "__main__":
    main()
