"""
SQLite implementation of repository interfaces
Following the Repository Pattern and Dependency Inversion Principle
"""

import sqlite3
import json
import uuid
from datetime import datetime
from typing import List, Optional, Dict, Any
from pathlib import Path

from .interfaces import ITargetRepository, IScanResultRepository, IConfigurationRepository
from ..core.models import Target, ScanResult, ScanConfiguration, ContactInfo
from ..core.exceptions import DatabaseError
from ..config.settings import get_config


class SQLiteTargetRepository(ITargetRepository):
    """SQLite implementation of target repository"""
    
    def __init__(self, db_path: Optional[str] = None):
        self.db_path = db_path or get_config().database.path
        self._init_database()
    
    def _init_database(self):
        """Initialize database tables"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS targets (
                        id TEXT PRIMARY KEY,
                        full_name TEXT NOT NULL,
                        username TEXT NOT NULL UNIQUE,
                        email TEXT NOT NULL,
                        phone TEXT NOT NULL,
                        image_data BLOB,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                conn.commit()
        except sqlite3.Error as e:
            raise DatabaseError("initialization", str(e), e)
    
    def save(self, target: Target) -> str:
        """Save a target and return its ID"""
        target_id = str(uuid.uuid4())
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    INSERT OR REPLACE INTO targets 
                    (id, full_name, username, email, phone, image_data, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (
                    target_id,
                    target.full_name,
                    target.username,
                    target.email,
                    target.phone,
                    target.image_data,
                    target.created_at.isoformat()
                ))
                conn.commit()
                return target_id
        except sqlite3.Error as e:
            raise DatabaseError("save", str(e), e)
    
    def get_by_id(self, target_id: str) -> Optional[Target]:
        """Get target by ID"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.execute(
                    "SELECT * FROM targets WHERE id = ?", (target_id,)
                )
                row = cursor.fetchone()
                return self._row_to_target(row) if row else None
        except sqlite3.Error as e:
            raise DatabaseError("get_by_id", str(e), e)
    
    def get_by_username(self, username: str) -> Optional[Target]:
        """Get target by username"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.execute(
                    "SELECT * FROM targets WHERE username = ?", (username,)
                )
                row = cursor.fetchone()
                return self._row_to_target(row) if row else None
        except sqlite3.Error as e:
            raise DatabaseError("get_by_username", str(e), e)
    
    def get_all(self) -> List[Target]:
        """Get all targets"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.execute("SELECT * FROM targets ORDER BY created_at DESC")
                rows = cursor.fetchall()
                return [self._row_to_target(row) for row in rows]
        except sqlite3.Error as e:
            raise DatabaseError("get_all", str(e), e)
    
    def delete(self, target_id: str) -> bool:
        """Delete a target"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("DELETE FROM targets WHERE id = ?", (target_id,))
                conn.commit()
                return cursor.rowcount > 0
        except sqlite3.Error as e:
            raise DatabaseError("delete", str(e), e)
    
    def _row_to_target(self, row: sqlite3.Row) -> Target:
        """Convert database row to Target object"""
        return Target(
            full_name=row['full_name'],
            username=row['username'],
            contact_info=ContactInfo(email=row['email'], phone=row['phone']),
            image_data=row['image_data'],
            created_at=datetime.fromisoformat(row['created_at'])
        )


class SQLiteScanResultRepository(IScanResultRepository):
    """SQLite implementation of scan result repository"""
    
    def __init__(self, db_path: Optional[str] = None):
        self.db_path = db_path or get_config().database.path
        self._init_database()
    
    def _init_database(self):
        """Initialize database tables"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS scan_results (
                        id TEXT PRIMARY KEY,
                        target_username TEXT NOT NULL,
                        status TEXT NOT NULL,
                        started_at TIMESTAMP,
                        completed_at TIMESTAMP,
                        error_message TEXT,
                        sherlock_result TEXT,
                        email_analysis TEXT,
                        phone_analysis TEXT,
                        image_analysis TEXT,
                        social_media_scan TEXT,
                        threat_assessment TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                conn.commit()
        except sqlite3.Error as e:
            raise DatabaseError("initialization", str(e), e)
    
    def save(self, scan_result: ScanResult) -> str:
        """Save a scan result and return its ID"""
        result_id = str(uuid.uuid4())
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    INSERT OR REPLACE INTO scan_results 
                    (id, target_username, status, started_at, completed_at, error_message,
                     sherlock_result, email_analysis, phone_analysis, image_analysis,
                     social_media_scan, threat_assessment, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    result_id,
                    scan_result.target.username,
                    scan_result.status.value,
                    scan_result.started_at.isoformat() if scan_result.started_at else None,
                    scan_result.completed_at.isoformat() if scan_result.completed_at else None,
                    scan_result.error_message,
                    json.dumps(scan_result.sherlock_result.__dict__) if scan_result.sherlock_result else None,
                    json.dumps(scan_result.email_analysis.__dict__) if scan_result.email_analysis else None,
                    json.dumps(scan_result.phone_analysis.__dict__) if scan_result.phone_analysis else None,
                    json.dumps(scan_result.image_analysis.__dict__) if scan_result.image_analysis else None,
                    json.dumps(scan_result.social_media_scan.__dict__) if scan_result.social_media_scan else None,
                    json.dumps(scan_result.threat_assessment.__dict__) if scan_result.threat_assessment else None,
                    datetime.now().isoformat()
                ))
                conn.commit()
                return result_id
        except sqlite3.Error as e:
            raise DatabaseError("save", str(e), e)
    
    def get_by_id(self, result_id: str) -> Optional[ScanResult]:
        """Get scan result by ID"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.execute(
                    "SELECT * FROM scan_results WHERE id = ?", (result_id,)
                )
                row = cursor.fetchone()
                return self._row_to_scan_result(row) if row else None
        except sqlite3.Error as e:
            raise DatabaseError("get_by_id", str(e), e)
    
    def get_by_target(self, target: Target) -> List[ScanResult]:
        """Get all scan results for a target"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.execute(
                    "SELECT * FROM scan_results WHERE target_username = ? ORDER BY created_at DESC",
                    (target.username,)
                )
                rows = cursor.fetchall()
                return [self._row_to_scan_result(row) for row in rows]
        except sqlite3.Error as e:
            raise DatabaseError("get_by_target", str(e), e)
    
    def get_recent(self, limit: int = 10) -> List[ScanResult]:
        """Get recent scan results"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.execute(
                    "SELECT * FROM scan_results ORDER BY created_at DESC LIMIT ?",
                    (limit,)
                )
                rows = cursor.fetchall()
                return [self._row_to_scan_result(row) for row in rows]
        except sqlite3.Error as e:
            raise DatabaseError("get_recent", str(e), e)
    
    def delete_old(self, older_than: datetime) -> int:
        """Delete scan results older than specified date"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute(
                    "DELETE FROM scan_results WHERE created_at < ?",
                    (older_than.isoformat(),)
                )
                conn.commit()
                return cursor.rowcount
        except sqlite3.Error as e:
            raise DatabaseError("delete_old", str(e), e)
    
    def _row_to_scan_result(self, row: sqlite3.Row) -> ScanResult:
        """Convert database row to ScanResult object"""
        # This is a simplified version - in practice, you'd need to properly
        # deserialize all the complex objects from JSON
        # For now, returning a basic ScanResult
        from ..core.models import ScanStatus
        
        # Create a basic target (you'd normally fetch this from target repository)
        target = Target(
            full_name="Unknown",
            username=row['target_username'],
            contact_info=ContactInfo(email="unknown", phone="unknown")
        )
        
        return ScanResult(
            target=target,
            status=ScanStatus(row['status']),
            started_at=datetime.fromisoformat(row['started_at']) if row['started_at'] else None,
            completed_at=datetime.fromisoformat(row['completed_at']) if row['completed_at'] else None,
            error_message=row['error_message']
        )
