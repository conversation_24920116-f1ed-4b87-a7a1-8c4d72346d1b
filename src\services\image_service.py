"""
Image analysis service implementation
"""

import hashlib
from typing import List, Dict, Any

from .interfaces import IImageAnalysisService
from ..core.models import ImageAnalysis
from ..core.exceptions import ImageAnalysisError


class ImageAnalysisService(IImageAnalysisService):
    """Service for image analysis"""
    
    def analyze_image(self, image_data: bytes) -> ImageAnalysis:
        """Analyze image for metadata and reverse search"""
        try:
            image_hash = self.generate_hash(image_data)
            file_size = len(image_data)
            reverse_search_urls = self._get_reverse_search_urls()
            metadata = self._extract_metadata(image_data)
            
            return ImageAnalysis(
                image_hash=image_hash,
                file_size=file_size,
                reverse_search_urls=reverse_search_urls,
                metadata=metadata
            )
            
        except Exception as e:
            raise ImageAnalysisError(str(e), e)
    
    def generate_hash(self, image_data: bytes) -> str:
        """Generate hash for image"""
        return hashlib.md5(image_data).hexdigest()
    
    def _get_reverse_search_urls(self) -> List[str]:
        """Get reverse image search URLs"""
        return [
            "https://images.google.com",
            "https://yandex.com/images",
            "https://tineye.com",
            "https://www.bing.com/visualsearch"
        ]
    
    def _extract_metadata(self, image_data: bytes) -> Dict[str, Any]:
        """Extract image metadata"""
        # Basic metadata extraction
        metadata = {
            'size_bytes': len(image_data),
            'format': self._detect_format(image_data),
            'has_exif': self._has_exif_data(image_data)
        }
        
        return metadata
    
    def _detect_format(self, image_data: bytes) -> str:
        """Detect image format from header"""
        if image_data.startswith(b'\xff\xd8\xff'):
            return 'JPEG'
        elif image_data.startswith(b'\x89PNG\r\n\x1a\n'):
            return 'PNG'
        elif image_data.startswith(b'GIF87a') or image_data.startswith(b'GIF89a'):
            return 'GIF'
        elif image_data.startswith(b'RIFF') and b'WEBP' in image_data[:12]:
            return 'WEBP'
        else:
            return 'Unknown'
    
    def _has_exif_data(self, image_data: bytes) -> bool:
        """Check if image has EXIF data"""
        # Simple check for EXIF marker in JPEG
        if image_data.startswith(b'\xff\xd8\xff'):
            return b'Exif' in image_data[:1000]
        return False
