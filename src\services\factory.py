"""
Service factory for dependency injection and object creation
Following the Factory Pattern and Dependency Inversion Principle
"""

from typing import Dict, Any, Optional
from enum import Enum

from .interfaces import (
    ISherlockService, IEmailAnalysisService, IPhoneAnalysisService,
    IImageAnalysisService, ISocialMediaService, IAIAnalysisService,
    IScanOrchestrationService, IReportService, INotificationService
)
from .sherlock_service import SherlockService, MockSherlockService
from .email_service import EmailAnalysisService, HaveIBeenPwnedService
from .scan_orchestration_service import ScanOrchestrationService, MockScanOrchestrationService
from ..repositories.interfaces import IScanResultRepository, ITargetRepository
from ..repositories.sqlite_repository import SQLiteTargetRepository, SQLiteScanResultRepository
from ..config.settings import get_config


class ServiceMode(Enum):
    """Service implementation modes"""
    PRODUCTION = "production"
    MOCK = "mock"
    TEST = "test"


class ServiceFactory:
    """Factory for creating service instances with proper dependencies"""
    
    def __init__(self, mode: ServiceMode = ServiceMode.PRODUCTION):
        self.mode = mode
        self.config = get_config()
        self._service_cache: Dict[str, Any] = {}
    
    def create_sherlock_service(self) -> ISherlockService:
        """Create Sherlock service instance"""
        cache_key = "sherlock_service"
        
        if cache_key not in self._service_cache:
            if self.mode == ServiceMode.MOCK or self.mode == ServiceMode.TEST:
                self._service_cache[cache_key] = MockSherlockService()
            else:
                self._service_cache[cache_key] = SherlockService()
        
        return self._service_cache[cache_key]
    
    def create_email_service(self) -> IEmailAnalysisService:
        """Create email analysis service instance"""
        cache_key = "email_service"
        
        if cache_key not in self._service_cache:
            if self.mode == ServiceMode.MOCK or self.mode == ServiceMode.TEST:
                self._service_cache[cache_key] = EmailAnalysisService()
            elif self.config.api.hibp_api_key:
                self._service_cache[cache_key] = HaveIBeenPwnedService()
            else:
                self._service_cache[cache_key] = EmailAnalysisService()
        
        return self._service_cache[cache_key]
    
    def create_phone_service(self) -> IPhoneAnalysisService:
        """Create phone analysis service instance"""
        cache_key = "phone_service"
        
        if cache_key not in self._service_cache:
            from .phone_service import PhoneAnalysisService
            self._service_cache[cache_key] = PhoneAnalysisService()
        
        return self._service_cache[cache_key]
    
    def create_image_service(self) -> IImageAnalysisService:
        """Create image analysis service instance"""
        cache_key = "image_service"
        
        if cache_key not in self._service_cache:
            from .image_service import ImageAnalysisService
            self._service_cache[cache_key] = ImageAnalysisService()
        
        return self._service_cache[cache_key]
    
    def create_social_media_service(self) -> ISocialMediaService:
        """Create social media service instance"""
        cache_key = "social_media_service"
        
        if cache_key not in self._service_cache:
            from .social_media_service import SocialMediaService
            self._service_cache[cache_key] = SocialMediaService()
        
        return self._service_cache[cache_key]
    
    def create_ai_service(self) -> IAIAnalysisService:
        """Create AI analysis service instance"""
        cache_key = "ai_service"
        
        if cache_key not in self._service_cache:
            from .ai_service import AIAnalysisService, MockAIAnalysisService
            
            if self.mode == ServiceMode.MOCK or self.mode == ServiceMode.TEST:
                self._service_cache[cache_key] = MockAIAnalysisService()
            elif self.config.api.openai_api_key:
                self._service_cache[cache_key] = AIAnalysisService()
            else:
                self._service_cache[cache_key] = MockAIAnalysisService()
        
        return self._service_cache[cache_key]
    
    def create_target_repository(self) -> ITargetRepository:
        """Create target repository instance"""
        cache_key = "target_repository"
        
        if cache_key not in self._service_cache:
            self._service_cache[cache_key] = SQLiteTargetRepository()
        
        return self._service_cache[cache_key]
    
    def create_scan_result_repository(self) -> IScanResultRepository:
        """Create scan result repository instance"""
        cache_key = "scan_result_repository"
        
        if cache_key not in self._service_cache:
            self._service_cache[cache_key] = SQLiteScanResultRepository()
        
        return self._service_cache[cache_key]
    
    def create_scan_orchestration_service(self) -> IScanOrchestrationService:
        """Create scan orchestration service with all dependencies"""
        cache_key = "scan_orchestration_service"
        
        if cache_key not in self._service_cache:
            if self.mode == ServiceMode.MOCK or self.mode == ServiceMode.TEST:
                self._service_cache[cache_key] = MockScanOrchestrationService()
            else:
                # Create all dependencies
                sherlock_service = self.create_sherlock_service()
                email_service = self.create_email_service()
                phone_service = self.create_phone_service()
                image_service = self.create_image_service()
                social_service = self.create_social_media_service()
                ai_service = self.create_ai_service()
                scan_repository = self.create_scan_result_repository()
                
                self._service_cache[cache_key] = ScanOrchestrationService(
                    sherlock_service=sherlock_service,
                    email_service=email_service,
                    phone_service=phone_service,
                    image_service=image_service,
                    social_service=social_service,
                    ai_service=ai_service,
                    scan_repository=scan_repository
                )
        
        return self._service_cache[cache_key]
    
    def create_report_service(self) -> IReportService:
        """Create report service instance"""
        cache_key = "report_service"
        
        if cache_key not in self._service_cache:
            from .report_service import ReportService
            self._service_cache[cache_key] = ReportService()
        
        return self._service_cache[cache_key]
    
    def create_notification_service(self) -> INotificationService:
        """Create notification service instance"""
        cache_key = "notification_service"
        
        if cache_key not in self._service_cache:
            from .notification_service import NotificationService
            self._service_cache[cache_key] = NotificationService()
        
        return self._service_cache[cache_key]
    
    def clear_cache(self):
        """Clear service cache"""
        self._service_cache.clear()
    
    def get_all_services(self) -> Dict[str, Any]:
        """Get all created services"""
        return {
            'sherlock': self.create_sherlock_service(),
            'email': self.create_email_service(),
            'phone': self.create_phone_service(),
            'image': self.create_image_service(),
            'social_media': self.create_social_media_service(),
            'ai': self.create_ai_service(),
            'scan_orchestration': self.create_scan_orchestration_service(),
            'report': self.create_report_service(),
            'notification': self.create_notification_service(),
            'target_repository': self.create_target_repository(),
            'scan_result_repository': self.create_scan_result_repository()
        }


# Global factory instance
_factory_instance: Optional[ServiceFactory] = None


def get_service_factory(mode: ServiceMode = ServiceMode.PRODUCTION) -> ServiceFactory:
    """Get global service factory instance"""
    global _factory_instance
    
    if _factory_instance is None or _factory_instance.mode != mode:
        _factory_instance = ServiceFactory(mode)
    
    return _factory_instance


def reset_service_factory():
    """Reset global service factory (useful for testing)"""
    global _factory_instance
    _factory_instance = None
