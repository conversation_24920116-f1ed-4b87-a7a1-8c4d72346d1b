name: Feature request
description: Request a feature or enhancement
labels: ["enhancement"]
body:
  - type: markdown
    attributes:
      value: |
        Concise and thoughtful titles help other contributors find and add your requested feature.
  - type: textarea
    id: description
    attributes:
      label: Description
      description: Describe the feature you are requesting
      placeholder: I'd like <PERSON> to be able to do xyz
    validations:
      required: true
  - type: checkboxes
    id: terms
    attributes:
      label: Code of Conduct
      description: By submitting this issue, you agree to follow our [Code of Conduct](https://github.com/sherlock-project/sherlock/blob/master/docs/CODE_OF_CONDUCT.md). 
      options:
        - label: I agree to follow this project's Code of Conduct
          required: true
