[tox]
requires =
    tox >= 3
envlist =
    lint
    py313
    py312
    py311
    py310
    py39
    py38

[testenv]
description = Attempt to build and install the package
deps =
    coverage
    jsonschema
    pytest
allowlist_externals = coverage
commands =
    coverage run --source=sherlock_project --module pytest -v
    coverage report --show-missing

[testenv:offline]
deps =
    jsonschema
    pytest
commands =
    pytest -v -m "not online"

[testenv:lint]
description = Lint with Ruff
deps =
    ruff
commands =
    ruff check

[gh-actions]
python =
    3.12: py312
    3.11: py311
    3.10: py310
    3.9: py39
