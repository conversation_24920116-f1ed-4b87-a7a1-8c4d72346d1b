"""
Scan orchestration service - coordinates all scanning operations
Following the Facade pattern and Single Responsibility Principle
"""

import uuid
from typing import Dict, Any, Optional
from datetime import datetime
import threading
import time

from .interfaces import (
    IScanOrchestrationService, ISherlockService, IEmailAnalysisService,
    IPhoneAnalysisService, IImageAnalysisService, ISocialMediaService,
    IAIAnalysisService
)
from ..core.models import Target, ScanResult, ScanConfiguration, ScanStatus
from ..core.exceptions import ScanError
from ..repositories.interfaces import IScanResultRepository


class ScanOrchestrationService(IScanOrchestrationService):
    """Orchestrates complete OSINT scans"""
    
    def __init__(
        self,
        sherlock_service: ISherlockService,
        email_service: IEmailAnalysisService,
        phone_service: IPhoneAnalysisService,
        image_service: IImageAnalysisService,
        social_service: ISocialMediaService,
        ai_service: IAIAnalysisService,
        scan_repository: IScanResultRepository
    ):
        self.sherlock_service = sherlock_service
        self.email_service = email_service
        self.phone_service = phone_service
        self.image_service = image_service
        self.social_service = social_service
        self.ai_service = ai_service
        self.scan_repository = scan_repository
        
        # Track running scans
        self._running_scans: Dict[str, ScanResult] = {}
        self._scan_progress: Dict[str, Dict[str, Any]] = {}
        self._scan_threads: Dict[str, threading.Thread] = {}
    
    def execute_scan(self, target: Target, config: ScanConfiguration) -> ScanResult:
        """Execute complete scan"""
        scan_id = str(uuid.uuid4())
        
        # Initialize scan result
        scan_result = ScanResult(target=target)
        scan_result.mark_started()
        
        # Track the scan
        self._running_scans[scan_id] = scan_result
        self._scan_progress[scan_id] = {
            'total_steps': self._count_enabled_steps(config),
            'completed_steps': 0,
            'current_step': 'Starting scan...',
            'percentage': 0
        }
        
        try:
            # Execute scan steps based on configuration
            step_count = 0
            
            if config.include_sherlock:
                self._update_progress(scan_id, step_count, "Running Sherlock username enumeration...")
                scan_result.sherlock_result = self.sherlock_service.scan_username(target.username, config)
                step_count += 1
            
            if config.include_email_analysis:
                self._update_progress(scan_id, step_count, "Analyzing email...")
                scan_result.email_analysis = self.email_service.analyze_email(target.email)
                step_count += 1
            
            if config.include_phone_analysis:
                self._update_progress(scan_id, step_count, "Analyzing phone number...")
                scan_result.phone_analysis = self.phone_service.analyze_phone(target.phone)
                step_count += 1
            
            if config.include_image_analysis and target.image_data:
                self._update_progress(scan_id, step_count, "Analyzing image...")
                scan_result.image_analysis = self.image_service.analyze_image(target.image_data)
                step_count += 1
            
            if config.include_social_media:
                self._update_progress(scan_id, step_count, "Scanning social media...")
                scan_result.social_media_scan = self.social_service.scan_social_media(
                    target.username, target.full_name
                )
                step_count += 1
            
            if config.include_ai_analysis:
                self._update_progress(scan_id, step_count, "Generating AI analysis...")
                scan_result.threat_assessment = self.ai_service.generate_threat_assessment(scan_result)
                step_count += 1
            
            # Mark scan as completed
            scan_result.mark_completed()
            self._update_progress(scan_id, step_count, "Scan completed!")
            
            # Save scan result
            self.scan_repository.save(scan_result)
            
            return scan_result
            
        except Exception as e:
            scan_result.mark_failed(str(e))
            self._update_progress(scan_id, -1, f"Scan failed: {str(e)}")
            raise ScanError(f"Scan failed: {str(e)}", "SCAN_EXECUTION_ERROR") from e
        
        finally:
            # Clean up tracking
            self._running_scans.pop(scan_id, None)
            self._scan_progress.pop(scan_id, None)
            self._scan_threads.pop(scan_id, None)
    
    def execute_scan_async(self, target: Target, config: ScanConfiguration) -> str:
        """Execute scan asynchronously and return scan ID"""
        scan_id = str(uuid.uuid4())
        
        def run_scan():
            try:
                self.execute_scan(target, config)
            except Exception as e:
                # Error is already handled in execute_scan
                pass
        
        thread = threading.Thread(target=run_scan)
        self._scan_threads[scan_id] = thread
        thread.start()
        
        return scan_id
    
    def get_scan_progress(self, scan_id: str) -> Dict[str, Any]:
        """Get scan progress"""
        return self._scan_progress.get(scan_id, {
            'total_steps': 0,
            'completed_steps': 0,
            'current_step': 'Scan not found',
            'percentage': 0
        })
    
    def cancel_scan(self, scan_id: str) -> bool:
        """Cancel running scan"""
        if scan_id in self._running_scans:
            scan_result = self._running_scans[scan_id]
            scan_result.status = ScanStatus.CANCELLED
            scan_result.completed_at = datetime.now()
            
            # Stop the thread if it's running
            if scan_id in self._scan_threads:
                thread = self._scan_threads[scan_id]
                # Note: Python doesn't have a clean way to stop threads
                # In a real implementation, you'd use a cancellation token
                
            self._cleanup_scan(scan_id)
            return True
        
        return False
    
    def get_running_scans(self) -> Dict[str, ScanResult]:
        """Get all currently running scans"""
        return self._running_scans.copy()
    
    def _count_enabled_steps(self, config: ScanConfiguration) -> int:
        """Count the number of enabled scan steps"""
        steps = 0
        if config.include_sherlock:
            steps += 1
        if config.include_email_analysis:
            steps += 1
        if config.include_phone_analysis:
            steps += 1
        if config.include_image_analysis:
            steps += 1
        if config.include_social_media:
            steps += 1
        if config.include_ai_analysis:
            steps += 1
        return steps
    
    def _update_progress(self, scan_id: str, completed_steps: int, current_step: str):
        """Update scan progress"""
        if scan_id in self._scan_progress:
            progress = self._scan_progress[scan_id]
            progress['completed_steps'] = completed_steps
            progress['current_step'] = current_step
            
            if progress['total_steps'] > 0:
                progress['percentage'] = int((completed_steps / progress['total_steps']) * 100)
            else:
                progress['percentage'] = 100 if completed_steps == -1 else 0
    
    def _cleanup_scan(self, scan_id: str):
        """Clean up scan tracking data"""
        self._running_scans.pop(scan_id, None)
        self._scan_progress.pop(scan_id, None)
        self._scan_threads.pop(scan_id, None)


class MockScanOrchestrationService(IScanOrchestrationService):
    """Mock implementation for testing"""
    
    def execute_scan(self, target: Target, config: ScanConfiguration) -> ScanResult:
        """Mock scan execution"""
        scan_result = ScanResult(target=target)
        scan_result.mark_started()
        
        # Simulate some processing time
        time.sleep(1)
        
        scan_result.mark_completed()
        return scan_result
    
    def get_scan_progress(self, scan_id: str) -> Dict[str, Any]:
        """Mock progress"""
        return {
            'total_steps': 6,
            'completed_steps': 6,
            'current_step': 'Completed',
            'percentage': 100
        }
    
    def cancel_scan(self, scan_id: str) -> bool:
        """Mock cancellation"""
        return True
