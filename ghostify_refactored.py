#!/usr/bin/env python3
"""
Ghostify Me - Refactored OSINT Scanner
Clean architecture with proper separation of concerns
"""

import streamlit as st
import sys
import os
from pathlib import Path

# Add src to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from src.config.settings import get_config, validate_config
from src.services.factory import get_service_factory, ServiceMode
from src.ui.streamlit_presenter import StreamlitPresenter, InputValidator, UIState
from src.ui.controller import UIController
from src.core.exceptions import GhostifyError
from src.core.models import ScanConfiguration


class GhostifyApp:
    """Main application class following clean architecture principles"""
    
    def __init__(self):
        self.config = get_config()
        self.service_factory = get_service_factory(ServiceMode.PRODUCTION)
        self.controller = UIController()
        self.validator = InputValidator()
        self.ui_state = UIState()
        self.presenter = StreamlitPresenter(self.validator, self.ui_state)
    
    def run(self):
        """Run the main application"""
        try:
            # Validate configuration
            validate_config()
            
            # Configure Streamlit page
            self._configure_streamlit()
            
            # Main application flow
            self._run_main_flow()
            
        except GhostifyError as e:
            self.presenter.display_error(e)
        except Exception as e:
            st.error(f"Unexpected error: {str(e)}")
            st.error("Please check your configuration and try again.")
    
    def _configure_streamlit(self):
        """Configure Streamlit page settings"""
        st.set_page_config(
            page_title=self.config.ui.page_title,
            page_icon=self.config.ui.page_icon,
            layout="wide",
            initial_sidebar_state="expanded"
        )
    
    def _run_main_flow(self):
        """Run the main application flow"""
        # Display scan form and get user input
        user_input = self.presenter.display_scan_form()
        
        if user_input:
            # Create scan configuration
            scan_config = self._create_scan_configuration(user_input)
            
            try:
                # Start scan
                scan_id = self.controller.handle_scan_request(user_input, scan_config)
                self.ui_state.set_current_scan_id(scan_id)
                
                # Monitor scan progress
                self._monitor_scan_progress(scan_id)
                
            except Exception as e:
                self.presenter.display_error(e)
    
    def _create_scan_configuration(self, user_input: dict) -> ScanConfiguration:
        """Create scan configuration from user input"""
        return ScanConfiguration(
            include_sherlock=user_input.get('include_sherlock', True),
            include_email_analysis=user_input.get('include_breach_check', True),
            include_phone_analysis=True,  # Always enabled
            include_image_analysis=user_input.get('image') is not None,
            include_social_media=user_input.get('include_social_scan', True),
            include_ai_analysis=user_input.get('include_ai_analysis', True),
            anonymize_logs=user_input.get('anonymize_logs', True),
            scan_timeout=60
        )
    
    def _monitor_scan_progress(self, scan_id: str):
        """Monitor scan progress and display results"""
        import time
        
        # Create progress placeholders
        progress_placeholder = st.empty()
        
        # Poll for progress
        max_iterations = 120  # 2 minutes max
        iteration = 0
        
        while iteration < max_iterations:
            progress = self.controller.handle_progress_request(scan_id)
            
            with progress_placeholder.container():
                self.presenter.display_scan_progress(progress)
            
            # Check if scan is complete
            if progress.get('percentage', 0) >= 100:
                break
            
            time.sleep(1)
            iteration += 1
        
        # Clear progress display
        progress_placeholder.empty()
        
        # Get and display final results
        scan_results = self.ui_state.get_scan_results()
        if scan_id in scan_results:
            self.presenter.display_scan_results(scan_results[scan_id])
        else:
            self.presenter.display_success("Scan completed! Results will be available shortly.")


def main():
    """Main entry point"""
    app = GhostifyApp()
    app.run()


if __name__ == "__main__":
    main()
