"""
AI analysis service implementation
"""

from typing import Op<PERSON>

from .interfaces import IAIAnalysisService
from ..core.models import ScanR<PERSON>ult, Threat<PERSON><PERSON>sm<PERSON>, ThreatLevel
from ..core.exceptions import AIAnalysisError
from ..config.settings import get_config


class AIAnalysisService(IAIAnalysisService):
    """Service for AI-powered analysis using OpenAI"""
    
    def __init__(self):
        self.config = get_config().api
        self.api_key = self.config.openai_api_key
    
    def generate_threat_assessment(self, scan_result: ScanResult) -> ThreatAssessment:
        """Generate AI-powered threat assessment"""
        if not self.api_key:
            raise AIAnalysisError("OpenAI API key not configured")
        
        try:
            # Prepare data for analysis
            analysis_data = self._prepare_analysis_data(scan_result)
            
            # Generate assessment using OpenAI
            assessment_text = self._call_openai_api(analysis_data)
            
            # Parse the assessment
            return self._parse_assessment(assessment_text)
            
        except Exception as e:
            raise AIAnalysisError(str(e), e)
    
    def generate_summary(self, scan_result: <PERSON>an<PERSON><PERSON>ult) -> str:
        """Generate comprehensive summary"""
        if not self.api_key:
            return self._generate_fallback_summary(scan_result)
        
        try:
            analysis_data = self._prepare_analysis_data(scan_result)
            return self._call_openai_api(analysis_data, summary_mode=True)
        except Exception as e:
            return self._generate_fallback_summary(scan_result)
    
    def _prepare_analysis_data(self, scan_result: ScanResult) -> str:
        """Prepare scan data for AI analysis"""
        data_parts = []
        
        # Target information
        target = scan_result.target
        data_parts.append(f"TARGET: {target.full_name} ({target.username})")
        data_parts.append(f"EMAIL: {target.email}")
        data_parts.append(f"PHONE: {target.phone}")
        
        # Sherlock results
        if scan_result.sherlock_result:
            sherlock = scan_result.sherlock_result
            found_platforms = [r.platform_name for r in sherlock.results if r.is_found()]
            data_parts.append(f"FOUND ACCOUNTS: {', '.join(found_platforms)}")
            data_parts.append(f"PLATFORMS CHECKED: {sherlock.platforms_checked}")
        
        # Email analysis
        if scan_result.email_analysis:
            email = scan_result.email_analysis
            data_parts.append(f"EMAIL BREACHES: {email.breaches_found}")
            if email.breach_details:
                breach_names = [b.breach_name for b in email.breach_details]
                data_parts.append(f"BREACH SOURCES: {', '.join(breach_names)}")
        
        # Phone analysis
        if scan_result.phone_analysis:
            phone = scan_result.phone_analysis
            data_parts.append(f"PHONE COUNTRY: {phone.country_code}")
            data_parts.append(f"PHONE TYPE: {phone.line_type}")
        
        return "\n".join(data_parts)
    
    def _call_openai_api(self, data: str, summary_mode: bool = False) -> str:
        """Call OpenAI API for analysis"""
        try:
            import openai
            openai.api_key = self.api_key
            
            if summary_mode:
                prompt = f"""
                Analyze this OSINT data and provide a comprehensive summary:
                
                {data}
                
                Provide a detailed analysis including:
                1. Digital footprint assessment
                2. Privacy risks identified
                3. Recommended actions
                """
            else:
                prompt = f"""
                As a cybersecurity expert, analyze this OSINT data and provide a threat assessment:
                
                {data}
                
                Provide:
                1. Threat Level (CRITICAL/HIGH/MEDIUM/LOW/MINIMAL)
                2. Key vulnerabilities (list)
                3. Immediate actions needed (list)
                4. Long-term recommendations (list)
                
                Format as structured text.
                """
            
            response = openai.ChatCompletion.create(
                model="gpt-3.5-turbo",
                messages=[{"role": "user", "content": prompt}],
                max_tokens=1500,
                temperature=0.7
            )
            
            return response.choices[0].message.content.strip()
            
        except Exception as e:
            raise AIAnalysisError(f"OpenAI API call failed: {str(e)}", e)
    
    def _parse_assessment(self, assessment_text: str) -> ThreatAssessment:
        """Parse AI assessment text into structured data"""
        # Simple parsing - in production, you'd use more sophisticated NLP
        threat_level = ThreatLevel.MEDIUM  # Default
        
        if "CRITICAL" in assessment_text.upper():
            threat_level = ThreatLevel.CRITICAL
        elif "HIGH" in assessment_text.upper():
            threat_level = ThreatLevel.HIGH
        elif "LOW" in assessment_text.upper():
            threat_level = ThreatLevel.LOW
        elif "MINIMAL" in assessment_text.upper():
            threat_level = ThreatLevel.MINIMAL
        
        # Extract lists (simplified)
        vulnerabilities = self._extract_list_items(assessment_text, "vulnerabilities")
        immediate_actions = self._extract_list_items(assessment_text, "immediate")
        long_term = self._extract_list_items(assessment_text, "long-term")
        
        return ThreatAssessment(
            threat_level=threat_level,
            vulnerabilities=vulnerabilities,
            immediate_actions=immediate_actions,
            long_term_recommendations=long_term,
            confidence_score=0.8  # Mock confidence
        )
    
    def _extract_list_items(self, text: str, section: str) -> list:
        """Extract list items from text (simplified)"""
        # This is a very basic implementation
        # In production, you'd use proper NLP parsing
        lines = text.split('\n')
        items = []
        
        for line in lines:
            if line.strip().startswith('-') or line.strip().startswith('•'):
                items.append(line.strip()[1:].strip())
        
        return items[:5]  # Limit to 5 items
    
    def _generate_fallback_summary(self, scan_result: ScanResult) -> str:
        """Generate fallback summary without AI"""
        summary_parts = []
        
        summary_parts.append("DIGITAL FOOTPRINT ANALYSIS")
        summary_parts.append("=" * 30)
        
        target = scan_result.target
        summary_parts.append(f"Target: {target.full_name} ({target.username})")
        
        if scan_result.sherlock_result:
            found = len([r for r in scan_result.sherlock_result.results if r.is_found()])
            summary_parts.append(f"Found accounts: {found}")
        
        if scan_result.email_analysis and scan_result.email_analysis.breaches_found > 0:
            summary_parts.append(f"Email compromised in {scan_result.email_analysis.breaches_found} breaches")
        
        summary_parts.append("\nRecommendations:")
        summary_parts.append("- Review privacy settings on found accounts")
        summary_parts.append("- Enable two-factor authentication")
        summary_parts.append("- Monitor for data breaches")
        
        return "\n".join(summary_parts)


class MockAIAnalysisService(IAIAnalysisService):
    """Mock AI service for testing"""
    
    def generate_threat_assessment(self, scan_result: ScanResult) -> ThreatAssessment:
        """Generate mock threat assessment"""
        return ThreatAssessment(
            threat_level=ThreatLevel.MEDIUM,
            vulnerabilities=["Multiple social media accounts", "Email in data breaches"],
            immediate_actions=["Review privacy settings", "Enable 2FA"],
            long_term_recommendations=["Regular privacy audits", "Monitor breaches"],
            confidence_score=0.85
        )
    
    def generate_summary(self, scan_result: ScanResult) -> str:
        """Generate mock summary"""
        return f"""
        MOCK AI ANALYSIS for {scan_result.target.username}
        
        Digital footprint shows moderate exposure across multiple platforms.
        Recommend immediate privacy review and security hardening.
        """
