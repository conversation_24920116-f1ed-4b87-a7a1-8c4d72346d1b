"""
Test suite for core models
"""

import pytest
from datetime import datetime

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from src.core.models import (
    Target, ContactInfo, ScanResult, ScanStatus, 
    PlatformResult, AccountStatus, SherlocResult,
    EmailAnalysis, BreachInfo, ThreatAssessment, ThreatLevel
)
from src.core.exceptions import ValidationError


class TestContactInfo:
    """Test ContactInfo value object"""
    
    def test_valid_contact_info(self):
        """Test creating valid contact info"""
        contact = ContactInfo(email="<EMAIL>", phone="************")
        assert contact.email == "<EMAIL>"
        assert contact.phone == "************"
    
    def test_invalid_contact_info(self):
        """Test validation of contact info"""
        with pytest.raises(ValueError):
            ContactInfo(email="", phone="************")
        
        with pytest.raises(ValueError):
            ContactInfo(email="<EMAIL>", phone="")


class TestTarget:
    """Test Target entity"""
    
    def test_valid_target(self):
        """Test creating valid target"""
        contact = ContactInfo(email="<EMAIL>", phone="************")
        target = Target(
            full_name="John Doe",
            username="johndoe",
            contact_info=contact
        )
        
        assert target.full_name == "John Doe"
        assert target.username == "johndoe"
        assert target.email == "<EMAIL>"
        assert target.phone == "************"
        assert isinstance(target.created_at, datetime)
    
    def test_invalid_target(self):
        """Test target validation"""
        contact = ContactInfo(email="<EMAIL>", phone="************")
        
        with pytest.raises(ValueError):
            Target(full_name="", username="johndoe", contact_info=contact)
        
        with pytest.raises(ValueError):
            Target(full_name="John Doe", username="", contact_info=contact)


class TestPlatformResult:
    """Test PlatformResult value object"""
    
    def test_platform_result_creation(self):
        """Test creating platform result"""
        result = PlatformResult(
            platform_name="GitHub",
            url="https://github.com/johndoe",
            status=AccountStatus.FOUND,
            response_time=1.5
        )
        
        assert result.platform_name == "GitHub"
        assert result.url == "https://github.com/johndoe"
        assert result.status == AccountStatus.FOUND
        assert result.response_time == 1.5
        assert result.is_found() is True
    
    def test_platform_result_not_found(self):
        """Test platform result not found"""
        result = PlatformResult(
            platform_name="Twitter",
            url="https://twitter.com/johndoe",
            status=AccountStatus.NOT_FOUND
        )
        
        assert result.is_found() is False


class TestSherlocResult:
    """Test SherlocResult aggregate"""
    
    def test_sherlock_result_creation(self):
        """Test creating Sherlock result"""
        results = [
            PlatformResult("GitHub", "https://github.com/test", AccountStatus.FOUND),
            PlatformResult("Twitter", "https://twitter.com/test", AccountStatus.NOT_FOUND),
            PlatformResult("Instagram", "https://instagram.com/test", AccountStatus.FOUND)
        ]
        
        sherlock_result = SherlocResult(
            username="test",
            platforms_checked=3,
            platforms_found=2,
            results=results
        )
        
        assert sherlock_result.username == "test"
        assert sherlock_result.platforms_checked == 3
        assert sherlock_result.platforms_found == 2
        assert len(sherlock_result.results) == 3
    
    def test_get_found_platforms(self):
        """Test getting found platforms"""
        results = [
            PlatformResult("GitHub", "https://github.com/test", AccountStatus.FOUND),
            PlatformResult("Twitter", "https://twitter.com/test", AccountStatus.NOT_FOUND),
            PlatformResult("Instagram", "https://instagram.com/test", AccountStatus.FOUND)
        ]
        
        sherlock_result = SherlocResult(
            username="test",
            platforms_checked=3,
            platforms_found=2,
            results=results
        )
        
        found_platforms = sherlock_result.get_found_platforms()
        assert len(found_platforms) == 2
        assert all(p.is_found() for p in found_platforms)
    
    def test_success_rate(self):
        """Test success rate calculation"""
        results = [
            PlatformResult("GitHub", "https://github.com/test", AccountStatus.FOUND),
            PlatformResult("Twitter", "https://twitter.com/test", AccountStatus.NOT_FOUND),
            PlatformResult("Instagram", "https://instagram.com/test", AccountStatus.FOUND)
        ]
        
        sherlock_result = SherlocResult(
            username="test",
            platforms_checked=3,
            platforms_found=2,
            results=results
        )
        
        assert sherlock_result.get_success_rate() == 2/3


class TestEmailAnalysis:
    """Test EmailAnalysis aggregate"""
    
    def test_email_analysis_creation(self):
        """Test creating email analysis"""
        breach_info = BreachInfo(
            breach_name="LinkedIn",
            breach_date=datetime(2012, 5, 5),
            compromised_data=["email", "password"]
        )
        
        email_analysis = EmailAnalysis(
            email="<EMAIL>",
            breaches_found=1,
            breach_details=[breach_info],
            reputation_score=75.0,
            is_disposable=False
        )
        
        assert email_analysis.email == "<EMAIL>"
        assert email_analysis.breaches_found == 1
        assert len(email_analysis.breach_details) == 1
        assert email_analysis.reputation_score == 75.0
        assert email_analysis.is_disposable is False
        assert email_analysis.is_compromised() is True
    
    def test_email_not_compromised(self):
        """Test email not compromised"""
        email_analysis = EmailAnalysis(
            email="<EMAIL>",
            breaches_found=0,
            breach_details=[],
            reputation_score=95.0,
            is_disposable=False
        )
        
        assert email_analysis.is_compromised() is False


class TestScanResult:
    """Test ScanResult aggregate root"""
    
    def test_scan_result_lifecycle(self):
        """Test scan result lifecycle"""
        contact = ContactInfo(email="<EMAIL>", phone="************")
        target = Target(
            full_name="John Doe",
            username="johndoe",
            contact_info=contact
        )
        
        scan_result = ScanResult(target=target)
        
        # Initial state
        assert scan_result.status == ScanStatus.NOT_STARTED
        assert scan_result.started_at is None
        assert scan_result.completed_at is None
        
        # Mark started
        scan_result.mark_started()
        assert scan_result.status == ScanStatus.IN_PROGRESS
        assert scan_result.started_at is not None
        
        # Mark completed
        scan_result.mark_completed()
        assert scan_result.status == ScanStatus.COMPLETED
        assert scan_result.completed_at is not None
        
        # Check duration
        duration = scan_result.get_duration()
        assert duration is not None
        assert duration >= 0
    
    def test_scan_result_failure(self):
        """Test scan result failure"""
        contact = ContactInfo(email="<EMAIL>", phone="************")
        target = Target(
            full_name="John Doe",
            username="johndoe",
            contact_info=contact
        )
        
        scan_result = ScanResult(target=target)
        scan_result.mark_started()
        scan_result.mark_failed("Test error")
        
        assert scan_result.status == ScanStatus.FAILED
        assert scan_result.error_message == "Test error"
        assert scan_result.completed_at is not None


class TestThreatAssessment:
    """Test ThreatAssessment value object"""
    
    def test_threat_assessment_creation(self):
        """Test creating threat assessment"""
        threat = ThreatAssessment(
            threat_level=ThreatLevel.HIGH,
            vulnerabilities=["Multiple social accounts", "Email breaches"],
            immediate_actions=["Enable 2FA", "Change passwords"],
            long_term_recommendations=["Regular audits", "Monitor breaches"],
            confidence_score=0.85
        )
        
        assert threat.threat_level == ThreatLevel.HIGH
        assert len(threat.vulnerabilities) == 2
        assert len(threat.immediate_actions) == 2
        assert len(threat.long_term_recommendations) == 2
        assert threat.confidence_score == 0.85


if __name__ == "__main__":
    pytest.main([__file__])
