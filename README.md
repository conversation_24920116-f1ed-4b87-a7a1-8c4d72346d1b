# 👻 Ghostify Me - Comprehensive OSINT Scanner

A powerful digital footprint detection and analysis tool that helps identify and analyze online presence across multiple platforms and data sources.

## 🚀 Features

### Core Capabilities
- **🔍 Sherlock Integration**: Username enumeration across 400+ social platforms
- **📧 Email Intelligence**: Data breach checking and email analysis
- **📱 Phone Analysis**: Carrier and location information extraction
- **🖼️ Image Analysis**: Reverse image search guidance and metadata extraction
- **🌐 Social Media Deep Scan**: Comprehensive social platform analysis
- **🤖 AI-Powered Analysis**: GPT-powered threat assessment and privacy recommendations

### Advanced Features
- **📊 Real-time Progress Tracking**: Live updates during scanning process
- **📋 Comprehensive Reporting**: Detailed PDF reports with actionable insights
- **🗄️ Scan Logging**: SQLite database for tracking investigations
- **🔒 Privacy-Focused**: Educational tool for privacy awareness
- **🐳 Docker Support**: Easy deployment and distribution

## 🛠️ Installation

### Method 1: Docker (Recommended)

1. **Clone the repository:**
   ```bash
   git clone <your-repo-url>
   cd Ghostify
   ```

2. **Set up environment variables:**
   ```bash
   cp .env.example .env
   # Edit .env with your API keys
   ```

3. **Run with Docker Compose:**
   ```bash
   docker-compose up -d
   ```

4. **Access the application:**
   Open http://localhost:8501 in your browser

### Method 2: Local Installation

1. **Clone and setup:**
   ```bash
   git clone <your-repo-url>
   cd Ghostify
   pip install -r requirements.txt
   ```

2. **Set environment variables:**
   ```bash
   export OPENAI_API_KEY="your_api_key_here"
   ```

3. **Run the application:**
   ```bash
   streamlit run ghostify.py
   ```

## 🔧 Configuration

### Required API Keys
- **OpenAI API Key**: Required for AI-powered analysis
  - Get from: https://platform.openai.com/api-keys

### Optional API Keys (for enhanced features)
- **HaveIBeenPwned API Key**: Enhanced breach checking
- **Hunter.io API Key**: Email verification and analysis

## 📖 Usage

1. **Launch the application** and navigate to the web interface
2. **Enter target information:**
   - Full Name
   - Email Address
   - Username
   - Phone Number
   - Profile Image (optional)

3. **Click "🔎 Investigate"** to start the comprehensive scan

4. **Review results** in the organized tabs:
   - **AI Analysis**: Threat assessment and recommendations
   - **Sherlock Results**: Username enumeration findings
   - **Email Intel**: Breach analysis and email intelligence
   - **Phone Intel**: Phone number analysis
   - **Social Media**: Platform-specific findings

5. **Download PDF Report** for documentation and further analysis

## 🔍 OSINT Workflow

The application follows a comprehensive OSINT methodology:

1. **Initial Information Gathering**
2. **Sherlock Username Enumeration** (400+ platforms)
3. **Email Analysis** (Breach checking, reputation)
4. **Phone Number Analysis** (Carrier, location, type)
5. **Reverse Image Search** (Guidance and tools)
6. **Social Media Deep Scan** (Manual verification links)
7. **AI-Powered Threat Assessment**
8. **Comprehensive Report Generation**

## 🛡️ Privacy & Ethics

This tool is designed for:
- **Educational purposes**
- **Privacy awareness**
- **Security research**
- **Authorized penetration testing**

### Important Notes:
- Only use on targets you own or have explicit permission to investigate
- Respect privacy laws and regulations in your jurisdiction
- Use responsibly and ethically
- This tool is for defensive security purposes

## 🐳 Docker Commands

```bash
# Build and run
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down

# Rebuild after changes
docker-compose up -d --build
```

## 📁 Project Structure

```
Ghostify/
├── ghostify.py              # Main application
├── sherlock/                # Sherlock repository (auto-downloaded)
├── requirements.txt         # Python dependencies
├── Dockerfile              # Docker configuration
├── docker-compose.yml      # Docker Compose setup
├── .env.example            # Environment variables template
├── README.md               # This file
├── data/                   # Data storage
├── reports/                # Generated reports
└── ghostify_logs.db        # SQLite database
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## ⚠️ Disclaimer

This tool is provided for educational and research purposes only. Users are responsible for ensuring their use complies with applicable laws and regulations. The developers are not responsible for any misuse of this tool.

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- **Sherlock Project**: Username enumeration capabilities
- **OpenAI**: AI-powered analysis
- **Streamlit**: Web interface framework
- **OSINT Community**: Methodologies and best practices
