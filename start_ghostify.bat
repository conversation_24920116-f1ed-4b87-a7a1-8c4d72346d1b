@echo off
echo 👻 Ghostify Me - Windows Quick Start
echo =====================================

echo 📦 Installing dependencies...
python -m pip install streamlit requests openai fpdf2 Pillow --quiet

echo 📄 Checking environment...
if not exist .env (
    echo Creating .env file...
    echo # Add your OpenAI API key here > .env
    echo OPENAI_API_KEY=your_api_key_here >> .env
    echo ⚠️ Please edit .env file with your OpenAI API key
    pause
)

echo 🚀 Starting Ghostify Me...
python -m streamlit run ghostify.py --server.port=8501

pause
