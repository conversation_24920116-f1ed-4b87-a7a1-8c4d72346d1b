"""
Streamlit implementation of UI presenter
Following the Model-View-Presenter pattern
"""

from typing import Dict, Any, Optional
import streamlit as st

from .interfaces import IUIPresenter, IInputValidator, IUIState
from ..core.models import Target, ScanResult, ContactInfo
from ..core.exceptions import ValidationError


class StreamlitPresenter(IUIPresenter):
    """Streamlit implementation of UI presenter"""
    
    def __init__(self, validator: IInputValidator, state: IUIState):
        self.validator = validator
        self.state = state
    
    def display_scan_form(self) -> Dict[str, Any]:
        """Display scan input form and return user input"""
        st.title("👻 Ghostify Me — Comprehensive OSINT Scanner")
        st.markdown("### 🔍 Advanced Digital Footprint Analysis & Privacy Assessment")
        
        # Privacy warning
        self._display_privacy_warning()
        
        # Input form
        col1, col2 = st.columns([2, 1])
        
        with col1:
            st.markdown("#### 📝 Target Information")
            full_name = st.text_input("Full Name", help="Enter the full name of the target")
            email = st.text_input("Email Address", help="Primary email address to investigate")
            username = st.text_input("Username", help="Primary username/handle to search")
            phone = st.text_input("Phone Number", help="Phone number (include country code if international)")
            image = st.file_uploader("Upload Profile Image", type=["png","jpg","jpeg"], 
                                   help="Optional: Upload a profile image for reverse search analysis")
        
        with col2:
            st.markdown("#### ⚙️ Scan Options")
            scan_depth = st.selectbox("Scan Depth", ["Quick Scan", "Standard Scan", "Deep Scan"], index=1)
            include_sherlock = st.checkbox("Include Sherlock Username Scan", value=True)
            include_breach_check = st.checkbox("Include Breach Analysis", value=True)
            include_social_scan = st.checkbox("Include Social Media Scan", value=True)
            include_ai_analysis = st.checkbox("Include AI Analysis", value=True)
            
            # Privacy options
            st.markdown("#### 🔒 Privacy Options")
            anonymize_logs = st.checkbox("Anonymize data in logs", value=True)
            minimal_logging = st.checkbox("Minimal logging mode", value=True)
        
        # Scan button
        if st.button("🔎 Investigate"):
            input_data = {
                'full_name': full_name,
                'email': email,
                'username': username,
                'phone': phone,
                'image': image.getvalue() if image else None,
                'scan_depth': scan_depth,
                'include_sherlock': include_sherlock,
                'include_breach_check': include_breach_check,
                'include_social_scan': include_social_scan,
                'include_ai_analysis': include_ai_analysis,
                'anonymize_logs': anonymize_logs,
                'minimal_logging': minimal_logging
            }
            
            # Validate input
            errors = self.validator.validate_target_input(input_data)
            if errors:
                for field, error in errors.items():
                    st.error(f"{field}: {error}")
                return {}
            
            return input_data
        
        return {}
    
    def display_scan_progress(self, progress: Dict[str, Any]) -> None:
        """Display scan progress"""
        if not progress:
            return
        
        progress_bar = st.progress(progress.get('percentage', 0) / 100)
        status_text = st.empty()
        status_text.text(progress.get('current_step', 'Processing...'))
        
        # Store progress elements in session state for updates
        if 'progress_elements' not in st.session_state:
            st.session_state.progress_elements = {}
        
        st.session_state.progress_elements['bar'] = progress_bar
        st.session_state.progress_elements['text'] = status_text
    
    def display_scan_results(self, scan_result: ScanResult) -> None:
        """Display scan results"""
        st.success("🎯 Investigation Complete!")
        
        # Display results in tabs
        tab1, tab2, tab3, tab4, tab5 = st.tabs([
            "🤖 AI Analysis", "🔍 Sherlock Results", "📧 Email Intel", 
            "📱 Phone Intel", "🌐 Social Media"
        ])
        
        with tab1:
            self._display_ai_analysis(scan_result)
        
        with tab2:
            self._display_sherlock_results(scan_result)
        
        with tab3:
            self._display_email_analysis(scan_result)
        
        with tab4:
            self._display_phone_analysis(scan_result)
        
        with tab5:
            self._display_social_media_results(scan_result)
        
        # Security recommendations
        self._display_security_recommendations()
    
    def display_error(self, error: Exception) -> None:
        """Display error message"""
        st.error(f"❌ Error: {str(error)}")
        
        # Show details in expander for debugging
        with st.expander("Error Details"):
            st.code(str(error))
    
    def display_success(self, message: str) -> None:
        """Display success message"""
        st.success(f"✅ {message}")
    
    def _display_privacy_warning(self) -> None:
        """Display privacy warning in sidebar"""
        with st.sidebar:
            st.markdown("## ⚠️ Privacy & Legal Notice")
            st.warning("""
            **IMPORTANT**: This tool is for educational and authorized security research only.
            
            ✅ **Authorized Use:**
            - Your own accounts/data
            - Authorized penetration testing
            - Educational purposes
            - Privacy awareness
            
            ❌ **Prohibited Use:**
            - Unauthorized surveillance
            - Stalking or harassment
            - Illegal investigations
            - Privacy violations
            """)
            
            consent = st.checkbox("I understand and agree to use this tool responsibly and legally")
            if not consent:
                st.stop()
    
    def _display_ai_analysis(self, scan_result: ScanResult) -> None:
        """Display AI analysis results"""
        st.markdown("## 🤖 AI-Powered Threat Assessment")
        
        if scan_result.threat_assessment:
            threat = scan_result.threat_assessment
            
            # Threat level with color coding
            level_colors = {
                'CRITICAL': '🔴',
                'HIGH': '🟠', 
                'MEDIUM': '🟡',
                'LOW': '🟢',
                'MINIMAL': '🔵'
            }
            
            threat_emoji = level_colors.get(threat.threat_level.value.upper(), '⚪')
            st.markdown(f"### {threat_emoji} Threat Level: {threat.threat_level.value.upper()}")
            
            if threat.vulnerabilities:
                st.markdown("#### 🎯 Key Vulnerabilities")
                for vuln in threat.vulnerabilities:
                    st.markdown(f"- {vuln}")
            
            if threat.immediate_actions:
                st.markdown("#### ⚡ Immediate Actions")
                for action in threat.immediate_actions:
                    st.markdown(f"- {action}")
            
            if threat.long_term_recommendations:
                st.markdown("#### 📋 Long-term Recommendations")
                for rec in threat.long_term_recommendations:
                    st.markdown(f"- {rec}")
        else:
            st.info("AI analysis not available")
    
    def _display_sherlock_results(self, scan_result: ScanResult) -> None:
        """Display Sherlock results"""
        st.markdown("## 🔍 Sherlock Username Enumeration Results")
        
        if scan_result.sherlock_result:
            sherlock = scan_result.sherlock_result
            found_accounts = sherlock.get_found_platforms()
            
            if found_accounts:
                st.success(f"✅ Found {len(found_accounts)} confirmed accounts!")
                for platform in found_accounts:
                    st.markdown(f"- **{platform.platform_name}**: [{platform.url}]({platform.url})")
            else:
                st.info("No confirmed accounts found with this username.")
            
            # Show all checked platforms
            with st.expander("View All Checked Platforms"):
                for platform in sherlock.results:
                    status_emoji = "✅" if platform.is_found() else "❌"
                    st.markdown(f"{status_emoji} **{platform.platform_name}**: {platform.status.value}")
        else:
            st.warning("Sherlock scan not available")
    
    def _display_email_analysis(self, scan_result: ScanResult) -> None:
        """Display email analysis"""
        st.markdown("## 📧 Email Intelligence")
        
        if scan_result.email_analysis:
            email = scan_result.email_analysis
            
            if email.is_compromised():
                st.error(f"🚨 Email found in {email.breaches_found} data breaches!")
                st.markdown("**Breaches found in:**")
                for breach in email.breach_details:
                    st.markdown(f"- {breach.breach_name}")
            else:
                st.success("✅ No known data breaches found for this email.")
            
            if email.reputation_score is not None:
                st.metric("Reputation Score", f"{email.reputation_score:.1f}/100")
        else:
            st.info("Email analysis not available")
    
    def _display_phone_analysis(self, scan_result: ScanResult) -> None:
        """Display phone analysis"""
        st.markdown("## 📱 Phone Number Intelligence")
        
        if scan_result.phone_analysis:
            phone = scan_result.phone_analysis
            
            col1, col2 = st.columns(2)
            with col1:
                st.markdown(f"**Formatted Number:** {phone.formatted_number}")
                st.markdown(f"**Country Code:** {phone.country_code}")
            with col2:
                st.markdown(f"**Carrier:** {phone.carrier or 'Unknown'}")
                st.markdown(f"**Type:** {phone.line_type or 'Unknown'}")
        else:
            st.info("Phone analysis not available")
    
    def _display_social_media_results(self, scan_result: ScanResult) -> None:
        """Display social media results"""
        st.markdown("## 🌐 Social Media Deep Scan")
        
        if scan_result.social_media_scan:
            social = scan_result.social_media_scan
            
            st.markdown(f"**Platforms Analyzed:** {social.platforms_checked}")
            
            st.markdown("### Manual Verification Required:")
            st.info("Click each link to manually verify account existence and gather intelligence.")
            
            for platform, url in social.manual_verification_urls.items():
                st.markdown(f"- **{platform.title()}**: [{url}]({url})")
            
            if social.recommendations:
                st.markdown("### 🕵️ Investigation Recommendations:")
                for rec in social.recommendations[:5]:  # Show first 5
                    st.markdown(f"- {rec}")
        else:
            st.info("Social media scan not available")
    
    def _display_security_recommendations(self) -> None:
        """Display security recommendations"""
        with st.expander("🛡️ Security & Privacy Recommendations"):
            st.markdown("""
            ### Immediate Actions:
            1. **Review Privacy Settings** on all found accounts
            2. **Enable Two-Factor Authentication** where available
            3. **Update Passwords** for compromised accounts
            4. **Remove Unused Accounts** to reduce attack surface
            
            ### Long-term Security:
            1. **Regular Privacy Audits** (quarterly)
            2. **Monitor Data Breaches** (use HaveIBeenPwned alerts)
            3. **Limit Information Sharing** on social platforms
            4. **Use Privacy-Focused Services** when possible
            """)


class InputValidator(IInputValidator):
    """Input validator implementation"""
    
    def validate_target_input(self, input_data: Dict[str, Any]) -> Dict[str, str]:
        """Validate target input and return errors"""
        errors = {}
        
        if not input_data.get('full_name', '').strip():
            errors['Full Name'] = 'Full name is required'
        
        if not input_data.get('email', '').strip():
            errors['Email'] = 'Email is required'
        elif not self._is_valid_email(input_data['email']):
            errors['Email'] = 'Invalid email format'
        
        if not input_data.get('username', '').strip():
            errors['Username'] = 'Username is required'
        
        if not input_data.get('phone', '').strip():
            errors['Phone'] = 'Phone number is required'
        
        return errors
    
    def validate_configuration(self, config) -> Dict[str, str]:
        """Validate scan configuration"""
        # Add configuration validation logic here
        return {}
    
    def _is_valid_email(self, email: str) -> bool:
        """Basic email validation"""
        import re
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(pattern, email) is not None


class UIState(IUIState):
    """UI state management using Streamlit session state"""
    
    def get_current_scan_id(self) -> Optional[str]:
        """Get current scan ID"""
        return st.session_state.get('current_scan_id')
    
    def set_current_scan_id(self, scan_id: str) -> None:
        """Set current scan ID"""
        st.session_state.current_scan_id = scan_id
    
    def get_scan_results(self) -> Dict[str, ScanResult]:
        """Get all scan results"""
        return st.session_state.get('scan_results', {})
    
    def add_scan_result(self, scan_id: str, result: ScanResult) -> None:
        """Add scan result"""
        if 'scan_results' not in st.session_state:
            st.session_state.scan_results = {}
        st.session_state.scan_results[scan_id] = result
    
    def clear_state(self) -> None:
        """Clear UI state"""
        for key in list(st.session_state.keys()):
            del st.session_state[key]
