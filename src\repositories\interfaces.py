"""
Repository interfaces following the Repository Pattern and Interface Segregation Principle
"""

from abc import ABC, abstractmethod
from typing import List, Optional, Dict, Any
from datetime import datetime

from ..core.models import Target, ScanResult, ScanConfiguration


class ITargetRepository(ABC):
    """Interface for target data operations"""
    
    @abstractmethod
    def save(self, target: Target) -> str:
        """Save a target and return its ID"""
        pass
    
    @abstractmethod
    def get_by_id(self, target_id: str) -> Optional[Target]:
        """Get target by ID"""
        pass
    
    @abstractmethod
    def get_by_username(self, username: str) -> Optional[Target]:
        """Get target by username"""
        pass
    
    @abstractmethod
    def get_all(self) -> List[Target]:
        """Get all targets"""
        pass
    
    @abstractmethod
    def delete(self, target_id: str) -> bool:
        """Delete a target"""
        pass


class IScanResultRepository(ABC):
    """Interface for scan result data operations"""
    
    @abstractmethod
    def save(self, scan_result: ScanResult) -> str:
        """Save a scan result and return its ID"""
        pass
    
    @abstractmethod
    def get_by_id(self, result_id: str) -> Optional[ScanResult]:
        """Get scan result by ID"""
        pass
    
    @abstractmethod
    def get_by_target(self, target: Target) -> List[ScanResult]:
        """Get all scan results for a target"""
        pass
    
    @abstractmethod
    def get_recent(self, limit: int = 10) -> List[ScanResult]:
        """Get recent scan results"""
        pass
    
    @abstractmethod
    def delete_old(self, older_than: datetime) -> int:
        """Delete scan results older than specified date"""
        pass


class IConfigurationRepository(ABC):
    """Interface for configuration data operations"""
    
    @abstractmethod
    def save_scan_config(self, config: ScanConfiguration) -> str:
        """Save scan configuration"""
        pass
    
    @abstractmethod
    def get_default_config(self) -> ScanConfiguration:
        """Get default scan configuration"""
        pass
    
    @abstractmethod
    def get_user_configs(self) -> List[ScanConfiguration]:
        """Get user-defined configurations"""
        pass


class IExternalAPIRepository(ABC):
    """Interface for external API operations"""
    
    @abstractmethod
    def make_request(self, url: str, method: str = "GET", **kwargs) -> Dict[str, Any]:
        """Make HTTP request to external API"""
        pass
    
    @abstractmethod
    def is_rate_limited(self, service: str) -> bool:
        """Check if service is rate limited"""
        pass
    
    @abstractmethod
    def record_api_call(self, service: str, endpoint: str, success: bool) -> None:
        """Record API call for rate limiting and monitoring"""
        pass


class ICacheRepository(ABC):
    """Interface for caching operations"""
    
    @abstractmethod
    def get(self, key: str) -> Optional[Any]:
        """Get cached value"""
        pass
    
    @abstractmethod
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """Set cached value with optional TTL"""
        pass
    
    @abstractmethod
    def delete(self, key: str) -> bool:
        """Delete cached value"""
        pass
    
    @abstractmethod
    def clear(self) -> None:
        """Clear all cached values"""
        pass


class IFileRepository(ABC):
    """Interface for file operations"""
    
    @abstractmethod
    def save_report(self, content: bytes, filename: str) -> str:
        """Save report file and return path"""
        pass
    
    @abstractmethod
    def get_report(self, filename: str) -> Optional[bytes]:
        """Get report file content"""
        pass
    
    @abstractmethod
    def delete_report(self, filename: str) -> bool:
        """Delete report file"""
        pass
    
    @abstractmethod
    def list_reports(self) -> List[str]:
        """List all report files"""
        pass
