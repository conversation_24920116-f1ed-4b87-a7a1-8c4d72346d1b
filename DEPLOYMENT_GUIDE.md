# 🚀 Ghostify Me - Deployment Guide

## 📋 Project Overview

**Ghostify Me** is a comprehensive digital footprint detection and analysis tool that helps identify and analyze online presence across multiple platforms. The project includes both a full-featured Streamlit web application and a basic command-line version.

## 🎯 What's Been Built

### ✅ Core Features Implemented
- **🔍 Sherlock Integration**: Username enumeration across 400+ social platforms
- **📧 Email Intelligence**: Data breach checking and email analysis  
- **📱 Phone Analysis**: Carrier and location information extraction
- **🖼️ Image Analysis**: Reverse image search guidance and metadata extraction
- **🌐 Social Media Deep Scan**: Comprehensive social platform analysis
- **🤖 AI-Powered Analysis**: GPT-powered threat assessment and privacy recommendations
- **📊 Real-time Progress Tracking**: Live updates during scanning process
- **📋 Comprehensive Reporting**: Detailed PDF reports with actionable insights
- **🗄️ Scan Logging**: SQLite database for tracking investigations
- **🔒 Privacy-Focused**: Educational tool with privacy warnings and ethical guidelines

### 📁 Files Created

#### Main Applications
- `ghostify.py` - Full-featured Streamlit web application
- `ghostify_basic.py` - Command-line version (works without dependencies)

#### Installation & Setup
- `requirements.txt` - Full dependency list
- `requirements-minimal.txt` - Minimal dependencies
- `install_dependencies.py` - Smart dependency installer
- `setup.py` - Automated setup script
- `quick_start.py` - Quick installation and startup
- `start_ghostify.bat` - Windows batch file for easy startup

#### Testing
- `test_ghostify.py` - Comprehensive test suite
- `test_basic.py` - Basic functionality tests

#### Deployment
- `Dockerfile` - Docker container configuration
- `docker-compose.yml` - Docker Compose setup
- `.env.example` - Environment variables template

#### Documentation
- `README.md` - Comprehensive documentation
- `DEPLOYMENT_GUIDE.md` - This file

#### Utilities
- `run_ghostify.py` - Advanced launcher with multiple options

## 🚀 Quick Start Options

### Option 1: Basic Version (No Dependencies)
```bash
# Works with just Python standard library
python ghostify_basic.py
```

### Option 2: Web Application (Requires Dependencies)
```bash
# Install minimal dependencies
python quick_start.py

# Or manually:
pip install streamlit requests openai fpdf2 Pillow
streamlit run ghostify.py
```

### Option 3: Docker Deployment
```bash
# Copy environment file
cp .env.example .env
# Edit .env with your API keys

# Start with Docker
docker-compose up -d
```

### Option 4: Windows Users
```bash
# Double-click or run:
start_ghostify.bat
```

## 🔧 Dependency Issues Resolution

### Problem: NumPy/Pandas Compilation Errors
The project handles this gracefully:
- **Full version** falls back to basic functionality if pandas is missing
- **Basic version** works with zero external dependencies
- **Smart installer** tries precompiled packages first

### Solution Hierarchy:
1. **Try basic version first**: `python ghostify_basic.py`
2. **Install minimal deps**: `python quick_start.py`
3. **Use Docker**: Handles all dependencies automatically
4. **Manual installation**: Install packages one by one

## 📊 Feature Matrix

| Feature | Basic Version | Web App | Docker |
|---------|---------------|---------|--------|
| Username enumeration | ✅ Manual links | ✅ Sherlock integration | ✅ Full Sherlock |
| Email analysis | ✅ Basic links | ✅ AI-powered | ✅ AI-powered |
| Phone analysis | ✅ Basic parsing | ✅ Enhanced analysis | ✅ Enhanced analysis |
| Report generation | ✅ Text file | ✅ PDF + Web UI | ✅ PDF + Web UI |
| Database logging | ✅ SQLite | ✅ SQLite | ✅ SQLite |
| Privacy features | ✅ Basic warnings | ✅ Full privacy controls | ✅ Full privacy controls |
| Real-time progress | ❌ | ✅ | ✅ |
| AI analysis | ❌ | ✅ (with API key) | ✅ (with API key) |

## 🔑 API Keys Required

### Essential
- **OpenAI API Key**: For AI-powered analysis (web app only)
  - Get from: https://platform.openai.com/api-keys
  - Add to `.env` file: `OPENAI_API_KEY=your_key_here`

### Optional (Enhanced Features)
- **HaveIBeenPwned API**: Enhanced breach checking
- **Hunter.io API**: Email verification

## 🛡️ Security & Privacy

### Built-in Protections
- **Legal warnings** and consent requirements
- **Data anonymization** options
- **Minimal logging** modes
- **Auto-delete** report options
- **Privacy recommendations** for targets

### Ethical Guidelines
- ✅ Educational and authorized use only
- ✅ Privacy awareness and security research
- ❌ Unauthorized surveillance or stalking
- ❌ Illegal investigations

## 🐳 Docker Deployment

### Quick Docker Start
```bash
# Clone and setup
git clone <your-repo>
cd Ghostify
cp .env.example .env
# Edit .env with API keys

# Start services
docker-compose up -d

# Access at http://localhost:8501
```

### Docker Commands
```bash
# View logs
docker-compose logs -f

# Stop services  
docker-compose down

# Rebuild after changes
docker-compose up -d --build
```

## 🧪 Testing

### Run All Tests
```bash
python test_ghostify.py    # Full test suite
python test_basic.py       # Basic functionality only
```

### Manual Testing
```bash
# Test basic version
python ghostify_basic.py

# Test web app (if dependencies installed)
streamlit run ghostify.py
```

## 📈 Usage Statistics

The application tracks:
- Total scans performed
- Platforms checked
- Accounts found
- Reports generated

All stored in local SQLite database with privacy options.

## 🔄 Troubleshooting

### Common Issues

1. **Import Errors**
   - Use basic version: `python ghostify_basic.py`
   - Install minimal deps: `python quick_start.py`

2. **Streamlit Not Found**
   - Run: `pip install streamlit`
   - Or use: `python quick_start.py`

3. **OpenAI API Errors**
   - Check `.env` file has correct API key
   - Verify API key is valid and has credits

4. **Sherlock Issues**
   - App falls back to basic username enumeration
   - Run: `python setup.py` to download Sherlock

5. **Port Already in Use**
   - Change port: `streamlit run ghostify.py --server.port=8502`

## 🎉 Success Indicators

### Basic Version Working
- ✅ Generates OSINT links
- ✅ Creates text reports
- ✅ Logs to database

### Web App Working  
- ✅ Streamlit interface loads
- ✅ Progress bars show during scans
- ✅ Tabbed results display
- ✅ PDF reports download

### Full Integration Working
- ✅ Sherlock username enumeration
- ✅ AI-powered analysis
- ✅ Real-time progress tracking
- ✅ Comprehensive PDF reports

## 📞 Support

If you encounter issues:
1. Try the basic version first
2. Check the troubleshooting section
3. Review the test output
4. Use Docker for complex dependency issues

The project is designed to degrade gracefully - even if some features don't work, the core OSINT functionality remains available.
