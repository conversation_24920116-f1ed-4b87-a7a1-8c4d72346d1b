"""
Email analysis service implementation
Following Single Responsibility Principle
"""

import re
from typing import List, Dict, Any
from datetime import datetime

from .interfaces import IEmailAnalysisService
from ..core.models import EmailAnalysis, BreachInfo
from ..core.exceptions import EmailAnalysisError
from ..config.settings import get_config


class EmailAnalysisService(IEmailAnalysisService):
    """Service for email analysis and breach checking"""
    
    def __init__(self):
        self.config = get_config().api
    
    def analyze_email(self, email: str) -> EmailAnalysis:
        """Analyze email for breaches and reputation"""
        if not self._is_valid_email(email):
            raise EmailAnalysisError(email, "Invalid email format")
        
        try:
            # Check for breaches
            breaches = self.check_breaches(email)
            breach_details = [self._parse_breach(breach) for breach in breaches]
            
            # Check if disposable
            is_disposable = self._is_disposable_email(email)
            
            # Calculate reputation score (mock implementation)
            reputation_score = self._calculate_reputation(email, len(breaches), is_disposable)
            
            return EmailAnalysis(
                email=email,
                breaches_found=len(breaches),
                breach_details=breach_details,
                reputation_score=reputation_score,
                is_disposable=is_disposable
            )
            
        except Exception as e:
            raise EmailAnalysisError(email, str(e), e)
    
    def check_breaches(self, email: str) -> List[Dict[str, Any]]:
        """Check email against breach databases"""
        # This is a mock implementation
        # In a real implementation, you would call HaveIBeenPwned API
        
        common_domains = ['gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com']
        domain = email.split('@')[1].lower()
        
        if domain in common_domains:
            # Simulate finding breaches for common domains
            return [
                {
                    'name': 'Collection #1',
                    'date': '2019-01-07',
                    'compromised_data': ['email', 'password']
                },
                {
                    'name': 'LinkedIn',
                    'date': '2012-05-05',
                    'compromised_data': ['email', 'password']
                }
            ]
        
        return []
    
    def _is_valid_email(self, email: str) -> bool:
        """Validate email format"""
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(pattern, email) is not None
    
    def _is_disposable_email(self, email: str) -> bool:
        """Check if email is from a disposable email service"""
        disposable_domains = [
            '10minutemail.com', 'tempmail.org', 'guerrillamail.com',
            'mailinator.com', 'throwaway.email'
        ]
        domain = email.split('@')[1].lower()
        return domain in disposable_domains
    
    def _calculate_reputation(self, email: str, breach_count: int, is_disposable: bool) -> float:
        """Calculate email reputation score (0-100)"""
        score = 100.0
        
        # Reduce score for breaches
        score -= breach_count * 15
        
        # Reduce score for disposable emails
        if is_disposable:
            score -= 30
        
        # Reduce score for suspicious patterns
        if '+' in email.split('@')[0]:  # Plus addressing
            score -= 5
        
        if email.split('@')[0].isdigit():  # Numeric username
            score -= 10
        
        return max(0.0, min(100.0, score))
    
    def _parse_breach(self, breach_data: Dict[str, Any]) -> BreachInfo:
        """Parse breach data into BreachInfo object"""
        breach_date = None
        if 'date' in breach_data:
            try:
                breach_date = datetime.strptime(breach_data['date'], '%Y-%m-%d')
            except ValueError:
                pass
        
        return BreachInfo(
            breach_name=breach_data.get('name', 'Unknown'),
            breach_date=breach_date,
            compromised_data=breach_data.get('compromised_data', [])
        )


class HaveIBeenPwnedService(IEmailAnalysisService):
    """Real HaveIBeenPwned API implementation"""
    
    def __init__(self):
        self.config = get_config().api
        self.api_key = self.config.hibp_api_key
        self.base_url = "https://haveibeenpwned.com/api/v3"
    
    def analyze_email(self, email: str) -> EmailAnalysis:
        """Analyze email using HaveIBeenPwned API"""
        if not self.api_key:
            raise EmailAnalysisError(email, "HaveIBeenPwned API key not configured")
        
        # Implementation would make actual API calls
        # For now, falling back to mock service
        mock_service = EmailAnalysisService()
        return mock_service.analyze_email(email)
    
    def check_breaches(self, email: str) -> List[Dict[str, Any]]:
        """Check breaches using HaveIBeenPwned API"""
        if not self.api_key:
            return []
        
        # Implementation would make actual API calls
        # For now, returning empty list
        return []
