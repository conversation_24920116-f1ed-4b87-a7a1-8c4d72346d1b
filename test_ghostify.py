#!/usr/bin/env python3
"""
Test script for Ghostify Me application
Tests core functionality without requiring Streamlit UI
"""

import os
import sys
import tempfile
from datetime import datetime

# Add current directory to path for imports
sys.path.insert(0, os.getcwd())

def test_basic_functions():
    """Test basic OSINT functions"""
    print("🧪 Testing Ghostify Me Core Functions")
    print("=" * 50)
    
    # Test data
    test_username = "testuser123"
    test_email = "<EMAIL>"
    test_phone = "************"
    test_name = "<PERSON>"
    
    try:
        # Import functions from ghostify
        from ghostify import (
            check_email_breach,
            analyze_phone_number,
            reverse_image_search_info,
            social_media_deep_scan,
            osint_links
        )
        
        print("✅ Successfully imported Ghostify functions")
        
        # Test email breach checking
        print("\n📧 Testing email breach analysis...")
        breach_result = check_email_breach(test_email)
        print(f"   Breach check result: {breach_result}")
        
        # Test phone analysis
        print("\n📱 Testing phone number analysis...")
        phone_result = analyze_phone_number(test_phone)
        print(f"   Phone analysis result: {phone_result}")
        
        # Test image analysis
        print("\n🖼️ Testing image analysis...")
        image_result = reverse_image_search_info(None)
        print(f"   Image analysis result: {image_result}")
        
        # Test social media scan
        print("\n🌐 Testing social media deep scan...")
        social_result = social_media_deep_scan(test_username, test_name)
        print(f"   Social scan platforms: {social_result.get('platforms_checked', 0)}")
        
        # Test OSINT links generation
        print("\n🔗 Testing OSINT links generation...")
        for query_type in ["Username", "Email", "Name", "Phone"]:
            test_query = test_username if query_type == "Username" else test_email if query_type == "Email" else test_name if query_type == "Name" else test_phone
            links = osint_links(test_query, query_type)
            print(f"   {query_type} links generated: {len(links)}")
        
        print("\n✅ All basic function tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        return False

def test_database_logging():
    """Test database logging functionality"""
    print("\n🗄️ Testing database logging...")
    
    try:
        from ghostify import log_scan
        
        # Test logging
        log_scan("Test User", "<EMAIL>", "testuser", "************")
        print("✅ Database logging test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Database logging test failed: {str(e)}")
        return False

def test_pdf_generation():
    """Test PDF generation functionality"""
    print("\n📄 Testing PDF generation...")
    
    try:
        from ghostify import generate_pdf_report
        
        # Test PDF generation
        test_summary = "This is a test summary for PDF generation."
        pdf_file = generate_pdf_report(
            "Test User", 
            "<EMAIL>", 
            "testuser", 
            "************", 
            test_summary
        )
        
        if os.path.exists(pdf_file):
            print(f"✅ PDF generated successfully: {pdf_file}")
            # Clean up test file
            os.remove(pdf_file)
            return True
        else:
            print("❌ PDF file was not created")
            return False
            
    except Exception as e:
        print(f"❌ PDF generation test failed: {str(e)}")
        return False

def test_sherlock_integration():
    """Test Sherlock integration (if available)"""
    print("\n🔍 Testing Sherlock integration...")
    
    try:
        # Check if Sherlock directory exists
        if not os.path.exists("sherlock"):
            print("⚠️ Sherlock directory not found - skipping Sherlock test")
            return True
        
        from ghostify import run_sherlock_scan
        
        # Test with a simple username
        print("   Running Sherlock scan (this may take a moment)...")
        result = run_sherlock_scan("testuser123")
        
        if isinstance(result, dict):
            print(f"✅ Sherlock integration test passed! Checked {len(result)} platforms")
            return True
        else:
            print("⚠️ Sherlock returned unexpected result format")
            return False
            
    except Exception as e:
        print(f"⚠️ Sherlock integration test failed: {str(e)}")
        print("   This is expected if Sherlock dependencies are not installed")
        return True  # Don't fail the entire test suite

def check_dependencies():
    """Check if required dependencies are installed"""
    print("\n📦 Checking dependencies...")
    
    required_packages = [
        'streamlit',
        'requests',
        'fpdf2',
        'PIL',
        'pandas',
        'sqlite3'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'PIL':
                import PIL
            elif package == 'fpdf2':
                import fpdf
            elif package == 'sqlite3':
                import sqlite3
            else:
                __import__(package)
            print(f"   ✅ {package}")
        except ImportError:
            print(f"   ❌ {package} - MISSING")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️ Missing packages: {', '.join(missing_packages)}")
        print("   Run: pip install -r requirements.txt")
        return False
    else:
        print("\n✅ All required dependencies are installed!")
        return True

def main():
    """Run all tests"""
    print("👻 Ghostify Me - Test Suite")
    print("=" * 50)
    
    # Check dependencies first
    if not check_dependencies():
        print("\n❌ Dependency check failed. Please install missing packages.")
        return False
    
    # Run tests
    tests = [
        test_basic_functions,
        test_database_logging,
        test_pdf_generation,
        test_sherlock_integration
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Ghostify Me is ready to use.")
        print("\n🚀 To start the application, run:")
        print("   streamlit run ghostify.py")
        return True
    else:
        print("⚠️ Some tests failed. Check the output above for details.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
