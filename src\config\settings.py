"""
Configuration management for Ghostify Me OSINT Scanner
Following the Single Responsibility Principle and dependency injection
"""

import os
from dataclasses import dataclass, field
from typing import Optional, Dict, Any
from pathlib import Path

from ..core.exceptions import ConfigurationError


@dataclass
class DatabaseConfig:
    """Database configuration settings"""
    path: str = "ghostify_logs.db"
    timeout: int = 30
    check_same_thread: bool = False
    
    def get_connection_string(self) -> str:
        return f"sqlite:///{self.path}"


@dataclass
class APIConfig:
    """External API configuration"""
    openai_api_key: Optional[str] = None
    hibp_api_key: Optional[str] = None
    hunter_api_key: Optional[str] = None
    timeout: int = 30
    max_retries: int = 3
    retry_delay: float = 1.0
    
    def validate(self) -> None:
        """Validate API configuration"""
        if not self.openai_api_key:
            raise ConfigurationError("openai_api_key", "OpenAI API key is required for AI analysis")


@dataclass
class SherlockConfig:
    """Sherlock-specific configuration"""
    path: str = "sherlock"
    timeout: int = 10
    max_platforms: Optional[int] = None
    use_tor: bool = False
    unique_tor: bool = False
    proxy: Optional[str] = None
    
    def get_sherlock_path(self) -> Path:
        return Path(self.path)


@dataclass
class ScanConfig:
    """Default scan configuration"""
    default_timeout: int = 60
    max_concurrent_scans: int = 5
    enable_caching: bool = True
    cache_ttl: int = 3600  # 1 hour
    
    # Feature flags
    enable_sherlock: bool = True
    enable_email_analysis: bool = True
    enable_phone_analysis: bool = True
    enable_image_analysis: bool = True
    enable_social_media: bool = True
    enable_ai_analysis: bool = True


@dataclass
class SecurityConfig:
    """Security and privacy configuration"""
    anonymize_logs: bool = True
    auto_delete_reports: bool = False
    report_retention_days: int = 30
    use_proxy: bool = False
    proxy_url: Optional[str] = None
    rate_limit_enabled: bool = True
    max_requests_per_minute: int = 60


@dataclass
class UIConfig:
    """User interface configuration"""
    port: int = 8501
    host: str = "0.0.0.0"
    debug: bool = False
    theme: str = "dark"
    page_title: str = "Ghostify Me - OSINT Scanner"
    page_icon: str = "👻"


@dataclass
class LoggingConfig:
    """Logging configuration"""
    level: str = "INFO"
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    file_path: Optional[str] = "logs/ghostify.log"
    max_file_size: int = 10 * 1024 * 1024  # 10MB
    backup_count: int = 5


@dataclass
class AppConfig:
    """Main application configuration"""
    database: DatabaseConfig = field(default_factory=DatabaseConfig)
    api: APIConfig = field(default_factory=APIConfig)
    sherlock: SherlockConfig = field(default_factory=SherlockConfig)
    scan: ScanConfig = field(default_factory=ScanConfig)
    security: SecurityConfig = field(default_factory=SecurityConfig)
    ui: UIConfig = field(default_factory=UIConfig)
    logging: LoggingConfig = field(default_factory=LoggingConfig)
    
    def validate(self) -> None:
        """Validate all configuration sections"""
        self.api.validate()
        
        # Validate paths exist
        if not self.sherlock.get_sherlock_path().exists():
            print(f"Warning: Sherlock path {self.sherlock.path} does not exist")
        
        # Create directories if needed
        if self.logging.file_path:
            log_dir = Path(self.logging.file_path).parent
            log_dir.mkdir(parents=True, exist_ok=True)


class ConfigManager:
    """Configuration manager following Singleton pattern"""
    
    _instance: Optional['ConfigManager'] = None
    _config: Optional[AppConfig] = None
    
    def __new__(cls) -> 'ConfigManager':
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if self._config is None:
            self._config = self._load_config()
    
    def _load_config(self) -> AppConfig:
        """Load configuration from environment variables and defaults"""
        config = AppConfig()
        
        # Load API keys from environment
        config.api.openai_api_key = os.getenv("OPENAI_API_KEY")
        config.api.hibp_api_key = os.getenv("HIBP_API_KEY")
        config.api.hunter_api_key = os.getenv("HUNTER_API_KEY")
        
        # Load other settings from environment with defaults
        config.database.path = os.getenv("DATABASE_PATH", config.database.path)
        config.sherlock.path = os.getenv("SHERLOCK_PATH", config.sherlock.path)
        config.ui.port = int(os.getenv("STREAMLIT_SERVER_PORT", config.ui.port))
        config.ui.host = os.getenv("STREAMLIT_SERVER_ADDRESS", config.ui.host)
        config.ui.debug = os.getenv("DEBUG", "false").lower() == "true"
        
        # Security settings
        config.security.anonymize_logs = os.getenv("ANONYMIZE_LOGS", "true").lower() == "true"
        config.security.use_proxy = os.getenv("USE_PROXY", "false").lower() == "true"
        config.security.proxy_url = os.getenv("PROXY_URL")
        
        # Logging settings
        config.logging.level = os.getenv("LOG_LEVEL", config.logging.level)
        config.logging.file_path = os.getenv("LOG_FILE", config.logging.file_path)
        
        return config
    
    @property
    def config(self) -> AppConfig:
        """Get the current configuration"""
        if self._config is None:
            self._config = self._load_config()
        return self._config
    
    def reload(self) -> None:
        """Reload configuration from environment"""
        self._config = self._load_config()
    
    def update_config(self, updates: Dict[str, Any]) -> None:
        """Update configuration with new values"""
        # This would implement deep update of configuration
        # For now, just reload
        self.reload()


# Global configuration instance
config_manager = ConfigManager()


def get_config() -> AppConfig:
    """Get the global configuration instance"""
    return config_manager.config


def validate_config() -> None:
    """Validate the current configuration"""
    config = get_config()
    config.validate()
