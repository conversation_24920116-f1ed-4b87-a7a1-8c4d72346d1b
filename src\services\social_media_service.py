"""
Social media analysis service implementation
"""

from typing import Dict, List

from .interfaces import ISocialMediaService
from ..core.models import SocialMediaScan


class SocialMediaService(ISocialMediaService):
    """Service for social media analysis"""
    
    def scan_social_media(self, username: str, full_name: str) -> SocialMediaScan:
        """Scan social media platforms"""
        platform_urls = self.get_platform_urls(username)
        name_search_urls = self._get_name_search_urls(full_name)
        
        # Combine both types of URLs
        all_urls = {**platform_urls, **name_search_urls}
        
        recommendations = self._get_investigation_recommendations()
        
        return SocialMediaScan(
            platforms_checked=len(all_urls),
            manual_verification_urls=all_urls,
            recommendations=recommendations
        )
    
    def get_platform_urls(self, username: str) -> Dict[str, str]:
        """Get URLs for manual verification"""
        return {
            'facebook': f"https://www.facebook.com/{username}",
            'instagram': f"https://www.instagram.com/{username}/",
            'twitter': f"https://twitter.com/{username}",
            'linkedin': f"https://www.linkedin.com/in/{username}",
            'tiktok': f"https://www.tiktok.com/@{username}",
            'youtube': f"https://www.youtube.com/c/{username}",
            'reddit': f"https://www.reddit.com/user/{username}",
            'github': f"https://github.com/{username}",
            'pinterest': f"https://www.pinterest.com/{username}/",
            'snapchat': f"https://www.snapchat.com/add/{username}",
            'telegram': f"https://t.me/{username}",
            'discord': f"https://discord.com/users/{username}",
            'twitch': f"https://www.twitch.tv/{username}",
            'spotify': f"https://open.spotify.com/user/{username}",
            'soundcloud': f"https://soundcloud.com/{username}"
        }
    
    def _get_name_search_urls(self, full_name: str) -> Dict[str, str]:
        """Get name-based search URLs"""
        encoded_name = full_name.replace(' ', '%20')
        
        return {
            'facebook_name_search': f"https://www.facebook.com/search/people/?q={encoded_name}",
            'linkedin_name_search': f"https://www.linkedin.com/search/results/people/?keywords={encoded_name}",
            'google_name_search': f"https://www.google.com/search?q=\"{encoded_name}\"",
            'whitepages': f"https://www.whitepages.com/name/{full_name.replace(' ', '-')}",
            'spokeo': f"https://www.spokeo.com/search?q={encoded_name}"
        }
    
    def _get_investigation_recommendations(self) -> List[str]:
        """Get investigation recommendations"""
        return [
            "Check profile pictures for consistency across platforms",
            "Look for linked accounts in bio sections",
            "Analyze posting patterns and timestamps",
            "Check for mutual connections",
            "Look for location tags and check-ins",
            "Search for the username with quotes in search engines",
            "Use Google dorking: site:platform.com \"username\"",
            "Check for variations of the username",
            "Look for email addresses in public posts",
            "Check for phone numbers in contact information",
            "Analyze friend/follower lists for connections",
            "Look for consistent themes or interests",
            "Check for cross-platform content sharing",
            "Analyze metadata from posted images",
            "Look for professional networking profiles"
        ]
