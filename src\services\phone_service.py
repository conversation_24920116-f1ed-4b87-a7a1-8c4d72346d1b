"""
Phone analysis service implementation
"""

import re
from typing import Optional

from .interfaces import IPhoneAnalysisService
from ..core.models import PhoneAnalysis
from ..core.exceptions import PhoneAnalysisError


class PhoneAnalysisService(IPhoneAnalysisService):
    """Service for phone number analysis"""
    
    def analyze_phone(self, phone: str) -> PhoneAnalysis:
        """Analyze phone number"""
        try:
            cleaned = self._clean_phone_number(phone)
            formatted = self.format_phone(phone)
            country_code = self._detect_country_code(cleaned)
            carrier = self._detect_carrier(cleaned)
            line_type = self._detect_line_type(cleaned)
            is_valid = self._validate_phone_number(cleaned)
            
            return PhoneAnalysis(
                original_number=phone,
                formatted_number=formatted,
                country_code=country_code,
                carrier=carrier,
                line_type=line_type,
                is_valid=is_valid
            )
            
        except Exception as e:
            raise PhoneAnalysisError(phone, str(e), e)
    
    def format_phone(self, phone: str) -> str:
        """Format phone number"""
        cleaned = self._clean_phone_number(phone)
        
        if len(cleaned) == 10:
            # US format
            return f"({cleaned[:3]}) {cleaned[3:6]}-{cleaned[6:]}"
        elif len(cleaned) == 11 and cleaned.startswith('1'):
            # US with country code
            return f"+1 ({cleaned[1:4]}) {cleaned[4:7]}-{cleaned[7:]}"
        else:
            # International format
            return f"+{cleaned}"
    
    def _clean_phone_number(self, phone: str) -> str:
        """Remove all non-digit characters"""
        return re.sub(r'\D', '', phone)
    
    def _detect_country_code(self, cleaned_phone: str) -> str:
        """Detect country code"""
        if len(cleaned_phone) == 10:
            return "+1"  # Assume US for 10-digit numbers
        elif len(cleaned_phone) == 11 and cleaned_phone.startswith('1'):
            return "+1"
        elif cleaned_phone.startswith('44'):
            return "+44"  # UK
        elif cleaned_phone.startswith('33'):
            return "+33"  # France
        elif cleaned_phone.startswith('49'):
            return "+49"  # Germany
        else:
            return "Unknown"
    
    def _detect_carrier(self, cleaned_phone: str) -> Optional[str]:
        """Detect carrier (mock implementation)"""
        if len(cleaned_phone) >= 6:
            prefix = cleaned_phone[:6]
            # Mock carrier detection based on prefix
            carrier_map = {
                '555': 'Verizon',
                '123': 'AT&T',
                '456': 'T-Mobile',
                '789': 'Sprint'
            }
            return carrier_map.get(prefix[:3], "Unknown")
        return "Unknown"
    
    def _detect_line_type(self, cleaned_phone: str) -> Optional[str]:
        """Detect line type (mobile, landline, etc.)"""
        # Mock implementation
        if len(cleaned_phone) >= 3:
            area_code = cleaned_phone[:3]
            # Some area codes are typically mobile
            mobile_prefixes = ['555', '123', '456']
            if area_code in mobile_prefixes:
                return "Mobile"
            else:
                return "Landline"
        return "Unknown"
    
    def _validate_phone_number(self, cleaned_phone: str) -> bool:
        """Validate phone number format"""
        # Basic validation
        if len(cleaned_phone) < 7:
            return False
        if len(cleaned_phone) > 15:
            return False
        if not cleaned_phone.isdigit():
            return False
        return True
