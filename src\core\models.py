"""
Core domain models for Ghostify Me OSINT Scanner
Following Domain-Driven Design principles
"""

from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from typing import Dict, List, Optional, Any
from abc import ABC, abstractmethod


class ScanStatus(Enum):
    """Enumeration for scan status"""
    NOT_STARTED = "not_started"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class AccountStatus(Enum):
    """Enumeration for account status"""
    FOUND = "found"
    NOT_FOUND = "not_found"
    AVAILABLE = "available"
    UNKNOWN = "unknown"
    ERROR = "error"


class ThreatLevel(Enum):
    """Enumeration for threat levels"""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    MINIMAL = "minimal"


@dataclass
class ContactInfo:
    """Value object for contact information"""
    email: str
    phone: str
    
    def __post_init__(self):
        if not self.email or not self.phone:
            raise ValueError("Email and phone are required")


@dataclass
class Target:
    """Domain entity representing a target for investigation"""
    full_name: str
    username: str
    contact_info: ContactInfo
    image_data: Optional[bytes] = None
    created_at: datetime = field(default_factory=datetime.now)
    
    def __post_init__(self):
        if not self.full_name or not self.username:
            raise ValueError("Full name and username are required")
    
    @property
    def email(self) -> str:
        return self.contact_info.email
    
    @property
    def phone(self) -> str:
        return self.contact_info.phone


@dataclass
class PlatformResult:
    """Value object for individual platform scan result"""
    platform_name: str
    url: str
    status: AccountStatus
    response_time: float = 0.0
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def is_found(self) -> bool:
        return self.status == AccountStatus.FOUND


@dataclass
class SherlocResult:
    """Aggregate for Sherlock scan results"""
    username: str
    platforms_checked: int
    platforms_found: int
    results: List[PlatformResult] = field(default_factory=list)
    scan_duration: float = 0.0
    
    def get_found_platforms(self) -> List[PlatformResult]:
        return [result for result in self.results if result.is_found()]
    
    def get_success_rate(self) -> float:
        if self.platforms_checked == 0:
            return 0.0
        return self.platforms_found / self.platforms_checked


@dataclass
class BreachInfo:
    """Value object for data breach information"""
    breach_name: str
    breach_date: Optional[datetime] = None
    compromised_data: List[str] = field(default_factory=list)


@dataclass
class EmailAnalysis:
    """Aggregate for email analysis results"""
    email: str
    breaches_found: int
    breach_details: List[BreachInfo] = field(default_factory=list)
    reputation_score: Optional[float] = None
    is_disposable: bool = False
    
    def is_compromised(self) -> bool:
        return self.breaches_found > 0


@dataclass
class PhoneAnalysis:
    """Aggregate for phone number analysis"""
    original_number: str
    formatted_number: str
    country_code: str
    carrier: Optional[str] = None
    line_type: Optional[str] = None
    is_valid: bool = True


@dataclass
class ImageAnalysis:
    """Aggregate for image analysis results"""
    image_hash: str
    file_size: int
    reverse_search_urls: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class SocialMediaScan:
    """Aggregate for social media scanning results"""
    platforms_checked: int
    manual_verification_urls: Dict[str, str] = field(default_factory=dict)
    recommendations: List[str] = field(default_factory=list)


@dataclass
class ThreatAssessment:
    """Value object for AI-powered threat assessment"""
    threat_level: ThreatLevel
    vulnerabilities: List[str] = field(default_factory=list)
    immediate_actions: List[str] = field(default_factory=list)
    long_term_recommendations: List[str] = field(default_factory=list)
    confidence_score: float = 0.0


@dataclass
class ScanResult:
    """Aggregate root for complete scan results"""
    target: Target
    sherlock_result: Optional[SherlocResult] = None
    email_analysis: Optional[EmailAnalysis] = None
    phone_analysis: Optional[PhoneAnalysis] = None
    image_analysis: Optional[ImageAnalysis] = None
    social_media_scan: Optional[SocialMediaScan] = None
    threat_assessment: Optional[ThreatAssessment] = None
    status: ScanStatus = ScanStatus.NOT_STARTED
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    error_message: Optional[str] = None
    
    def mark_started(self):
        self.status = ScanStatus.IN_PROGRESS
        self.started_at = datetime.now()
    
    def mark_completed(self):
        self.status = ScanStatus.COMPLETED
        self.completed_at = datetime.now()
    
    def mark_failed(self, error_message: str):
        self.status = ScanStatus.FAILED
        self.completed_at = datetime.now()
        self.error_message = error_message
    
    def get_duration(self) -> Optional[float]:
        if self.started_at and self.completed_at:
            return (self.completed_at - self.started_at).total_seconds()
        return None


@dataclass
class ScanConfiguration:
    """Value object for scan configuration"""
    include_sherlock: bool = True
    include_email_analysis: bool = True
    include_phone_analysis: bool = True
    include_image_analysis: bool = True
    include_social_media: bool = True
    include_ai_analysis: bool = True
    scan_timeout: int = 60
    max_platforms: Optional[int] = None
    use_proxy: bool = False
    anonymize_logs: bool = True


# Abstract base classes for interfaces (following Interface Segregation Principle)

class IScannable(ABC):
    """Interface for scannable operations"""
    
    @abstractmethod
    def scan(self, target: Target, config: ScanConfiguration) -> ScanResult:
        pass


class IAnalyzable(ABC):
    """Interface for analyzable operations"""
    
    @abstractmethod
    def analyze(self, data: Any) -> Any:
        pass


class IReportable(ABC):
    """Interface for reportable operations"""
    
    @abstractmethod
    def generate_report(self, scan_result: ScanResult) -> str:
        pass
