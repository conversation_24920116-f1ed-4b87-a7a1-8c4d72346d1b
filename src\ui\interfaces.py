"""
UI interfaces for separating presentation from business logic
Following the Model-View-Presenter pattern
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, Callable

from ..core.models import Target, ScanResult, ScanConfiguration


class IUIPresenter(ABC):
    """Interface for UI presenter"""
    
    @abstractmethod
    def display_scan_form(self) -> Dict[str, Any]:
        """Display scan input form and return user input"""
        pass
    
    @abstractmethod
    def display_scan_progress(self, progress: Dict[str, Any]) -> None:
        """Display scan progress"""
        pass
    
    @abstractmethod
    def display_scan_results(self, scan_result: ScanResult) -> None:
        """Display scan results"""
        pass
    
    @abstractmethod
    def display_error(self, error: Exception) -> None:
        """Display error message"""
        pass
    
    @abstractmethod
    def display_success(self, message: str) -> None:
        """Display success message"""
        pass


class IUIView(ABC):
    """Interface for UI view"""
    
    @abstractmethod
    def render_header(self) -> None:
        """Render application header"""
        pass
    
    @abstractmethod
    def render_sidebar(self) -> None:
        """Render sidebar with options"""
        pass
    
    @abstractmethod
    def render_main_content(self, content: Dict[str, Any]) -> None:
        """Render main content area"""
        pass
    
    @abstractmethod
    def render_footer(self) -> None:
        """Render footer"""
        pass


class IInputValidator(ABC):
    """Interface for input validation"""
    
    @abstractmethod
    def validate_target_input(self, input_data: Dict[str, Any]) -> Dict[str, str]:
        """Validate target input and return errors"""
        pass
    
    @abstractmethod
    def validate_configuration(self, config: ScanConfiguration) -> Dict[str, str]:
        """Validate scan configuration"""
        pass


class IUIController(ABC):
    """Interface for UI controller"""
    
    @abstractmethod
    def handle_scan_request(self, target_data: Dict[str, Any], config: ScanConfiguration) -> str:
        """Handle scan request and return scan ID"""
        pass
    
    @abstractmethod
    def handle_progress_request(self, scan_id: str) -> Dict[str, Any]:
        """Handle progress request"""
        pass
    
    @abstractmethod
    def handle_cancel_request(self, scan_id: str) -> bool:
        """Handle scan cancellation"""
        pass
    
    @abstractmethod
    def handle_report_download(self, scan_result: ScanResult, format: str) -> bytes:
        """Handle report download"""
        pass


class IUIEventHandler(ABC):
    """Interface for UI event handling"""
    
    @abstractmethod
    def on_scan_started(self, scan_id: str) -> None:
        """Handle scan started event"""
        pass
    
    @abstractmethod
    def on_scan_progress(self, scan_id: str, progress: Dict[str, Any]) -> None:
        """Handle scan progress event"""
        pass
    
    @abstractmethod
    def on_scan_completed(self, scan_result: ScanResult) -> None:
        """Handle scan completed event"""
        pass
    
    @abstractmethod
    def on_scan_failed(self, scan_id: str, error: Exception) -> None:
        """Handle scan failed event"""
        pass


class IUIState(ABC):
    """Interface for UI state management"""
    
    @abstractmethod
    def get_current_scan_id(self) -> Optional[str]:
        """Get current scan ID"""
        pass
    
    @abstractmethod
    def set_current_scan_id(self, scan_id: str) -> None:
        """Set current scan ID"""
        pass
    
    @abstractmethod
    def get_scan_results(self) -> Dict[str, ScanResult]:
        """Get all scan results"""
        pass
    
    @abstractmethod
    def add_scan_result(self, scan_id: str, result: ScanResult) -> None:
        """Add scan result"""
        pass
    
    @abstractmethod
    def clear_state(self) -> None:
        """Clear UI state"""
        pass
