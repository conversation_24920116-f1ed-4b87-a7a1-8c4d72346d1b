"""
UI Controller implementation
Following the Model-View-Controller pattern
"""

from typing import Dict, Any
import uuid

from .interfaces import IUIController
from ..core.models import Target, ScanResult, ScanConfiguration, ContactInfo
from ..services.interfaces import IScanOrchestrationService, IReportService
from ..services.factory import get_service_factory


class UIController(IUIController):
    """Main UI controller that coordinates between UI and business logic"""
    
    def __init__(self):
        self.service_factory = get_service_factory()
        self.scan_service = self.service_factory.create_scan_orchestration_service()
        self.report_service = self.service_factory.create_report_service()
    
    def handle_scan_request(self, target_data: Dict[str, Any], config: ScanConfiguration) -> str:
        """Handle scan request and return scan ID"""
        try:
            # Create target from input data
            target = self._create_target_from_data(target_data)
            
            # Execute scan asynchronously
            scan_id = self.scan_service.execute_scan_async(target, config)
            
            return scan_id
            
        except Exception as e:
            raise e
    
    def handle_progress_request(self, scan_id: str) -> Dict[str, Any]:
        """Handle progress request"""
        return self.scan_service.get_scan_progress(scan_id)
    
    def handle_cancel_request(self, scan_id: str) -> bool:
        """Handle scan cancellation"""
        return self.scan_service.cancel_scan(scan_id)
    
    def handle_report_download(self, scan_result: ScanResult, format: str) -> bytes:
        """Handle report download"""
        if format.lower() == 'pdf':
            return self.report_service.generate_pdf_report(scan_result)
        elif format.lower() == 'json':
            import json
            report_data = self.report_service.generate_json_report(scan_result)
            return json.dumps(report_data, indent=2).encode('utf-8')
        else:
            text_report = self.report_service.generate_text_report(scan_result)
            return text_report.encode('utf-8')
    
    def _create_target_from_data(self, data: Dict[str, Any]) -> Target:
        """Create Target object from input data"""
        contact_info = ContactInfo(
            email=data['email'],
            phone=data['phone']
        )
        
        return Target(
            full_name=data['full_name'],
            username=data['username'],
            contact_info=contact_info,
            image_data=data.get('image')
        )
    
    def _create_config_from_data(self, data: Dict[str, Any]) -> ScanConfiguration:
        """Create ScanConfiguration from input data"""
        return ScanConfiguration(
            include_sherlock=data.get('include_sherlock', True),
            include_email_analysis=data.get('include_breach_check', True),
            include_phone_analysis=True,  # Always enabled
            include_image_analysis=data.get('image') is not None,
            include_social_media=data.get('include_social_scan', True),
            include_ai_analysis=data.get('include_ai_analysis', True),
            anonymize_logs=data.get('anonymize_logs', True)
        )
