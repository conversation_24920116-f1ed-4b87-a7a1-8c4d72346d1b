"""
Service interfaces following the Interface Segregation Principle
"""

from abc import ABC, abstractmethod
from typing import List, Optional, Dict, Any

from ..core.models import (
    Target, ScanResult, ScanConfiguration, SherlocResult, 
    EmailAnalysis, PhoneAnalysis, ImageAnalysis, 
    SocialMediaScan, ThreatAssessment
)


class ISherlockService(ABC):
    """Interface for Sherlock username enumeration service"""
    
    @abstractmethod
    def scan_username(self, username: str, config: ScanConfiguration) -> SherlocResult:
        """Scan username across platforms"""
        pass
    
    @abstractmethod
    def is_available(self) -> bool:
        """Check if <PERSON> is available"""
        pass


class IEmailAnalysisService(ABC):
    """Interface for email analysis service"""
    
    @abstractmethod
    def analyze_email(self, email: str) -> EmailAnalysis:
        """Analyze email for breaches and reputation"""
        pass
    
    @abstractmethod
    def check_breaches(self, email: str) -> List[Dict[str, Any]]:
        """Check email against breach databases"""
        pass


class IPhoneAnalysisService(ABC):
    """Interface for phone analysis service"""
    
    @abstractmethod
    def analyze_phone(self, phone: str) -> PhoneAnalysis:
        """Analyze phone number"""
        pass
    
    @abstractmethod
    def format_phone(self, phone: str) -> str:
        """Format phone number"""
        pass


class IImageAnalysisService(ABC):
    """Interface for image analysis service"""
    
    @abstractmethod
    def analyze_image(self, image_data: bytes) -> ImageAnalysis:
        """Analyze image for metadata and reverse search"""
        pass
    
    @abstractmethod
    def generate_hash(self, image_data: bytes) -> str:
        """Generate hash for image"""
        pass


class ISocialMediaService(ABC):
    """Interface for social media analysis service"""
    
    @abstractmethod
    def scan_social_media(self, username: str, full_name: str) -> SocialMediaScan:
        """Scan social media platforms"""
        pass
    
    @abstractmethod
    def get_platform_urls(self, username: str) -> Dict[str, str]:
        """Get URLs for manual verification"""
        pass


class IAIAnalysisService(ABC):
    """Interface for AI-powered analysis service"""
    
    @abstractmethod
    def generate_threat_assessment(self, scan_result: ScanResult) -> ThreatAssessment:
        """Generate AI-powered threat assessment"""
        pass
    
    @abstractmethod
    def generate_summary(self, scan_result: ScanResult) -> str:
        """Generate comprehensive summary"""
        pass


class IReportService(ABC):
    """Interface for report generation service"""
    
    @abstractmethod
    def generate_pdf_report(self, scan_result: ScanResult) -> bytes:
        """Generate PDF report"""
        pass
    
    @abstractmethod
    def generate_text_report(self, scan_result: ScanResult) -> str:
        """Generate text report"""
        pass
    
    @abstractmethod
    def generate_json_report(self, scan_result: ScanResult) -> Dict[str, Any]:
        """Generate JSON report"""
        pass


class IScanOrchestrationService(ABC):
    """Interface for scan orchestration service"""
    
    @abstractmethod
    def execute_scan(self, target: Target, config: ScanConfiguration) -> ScanResult:
        """Execute complete scan"""
        pass
    
    @abstractmethod
    def get_scan_progress(self, scan_id: str) -> Dict[str, Any]:
        """Get scan progress"""
        pass
    
    @abstractmethod
    def cancel_scan(self, scan_id: str) -> bool:
        """Cancel running scan"""
        pass


class INotificationService(ABC):
    """Interface for notification service"""
    
    @abstractmethod
    def notify_scan_complete(self, scan_result: ScanResult) -> None:
        """Notify when scan is complete"""
        pass
    
    @abstractmethod
    def notify_error(self, error: Exception, context: Dict[str, Any]) -> None:
        """Notify about errors"""
        pass
