#!/usr/bin/env python3
"""
Ghostify Me Setup Script
Automated setup for the comprehensive OSINT scanner
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"🔧 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed: {e}")
        print(f"Error output: {e.stderr}")
        return False

def check_requirements():
    """Check if required tools are installed"""
    print("🔍 Checking requirements...")
    
    # Check Python version
    if sys.version_info < (3, 9):
        print("❌ Python 3.9 or higher is required")
        return False
    
    # Check if git is available
    if not shutil.which('git'):
        print("❌ Git is required but not found")
        return False
    
    print("✅ Requirements check passed")
    return True

def setup_environment():
    """Set up the environment"""
    print("🌍 Setting up environment...")
    
    # Create necessary directories
    directories = ['data', 'reports', 'logs']
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"📁 Created directory: {directory}")
    
    # Copy environment file if it doesn't exist
    if not os.path.exists('.env'):
        if os.path.exists('.env.example'):
            shutil.copy('.env.example', '.env')
            print("📄 Created .env file from template")
            print("⚠️  Please edit .env file with your API keys")
        else:
            print("⚠️  .env.example not found")
    
    return True

def install_dependencies():
    """Install Python dependencies"""
    print("📦 Installing Python dependencies...")
    
    # Upgrade pip first
    if not run_command(f"{sys.executable} -m pip install --upgrade pip", "Upgrading pip"):
        return False
    
    # Install requirements
    if not run_command(f"{sys.executable} -m pip install -r requirements.txt", "Installing dependencies"):
        return False
    
    return True

def setup_sherlock():
    """Set up Sherlock if not already present"""
    print("🔍 Setting up Sherlock...")
    
    if not os.path.exists('sherlock'):
        if not run_command("git clone https://github.com/sherlock-project/sherlock.git", "Cloning Sherlock repository"):
            return False
    else:
        print("✅ Sherlock already exists")
    
    return True

def main():
    """Main setup function"""
    print("👻 Ghostify Me - Setup Script")
    print("=" * 40)
    
    # Check requirements
    if not check_requirements():
        sys.exit(1)
    
    # Setup environment
    if not setup_environment():
        sys.exit(1)
    
    # Install dependencies
    if not install_dependencies():
        sys.exit(1)
    
    # Setup Sherlock
    if not setup_sherlock():
        sys.exit(1)
    
    print("\n🎉 Setup completed successfully!")
    print("\n📋 Next steps:")
    print("1. Edit .env file with your API keys")
    print("2. Run: streamlit run ghostify.py")
    print("3. Or use Docker: docker-compose up -d")
    print("\n🔒 Remember to use this tool responsibly and ethically!")

if __name__ == "__main__":
    main()
