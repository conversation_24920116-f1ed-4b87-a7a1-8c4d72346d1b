#!/usr/bin/env python3
"""
Quick Start Script for Ghostify Me
Handles installation and startup automatically
"""

import subprocess
import sys
import os
import time

def install_minimal_deps():
    """Install only the essential dependencies"""
    print("📦 Installing minimal dependencies...")
    
    essential_packages = [
        "streamlit",
        "requests", 
        "openai",
        "fpdf2",
        "Pillow"
    ]
    
    for package in essential_packages:
        print(f"Installing {package}...")
        try:
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", package
            ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            print(f"✅ {package} installed")
        except subprocess.CalledProcessError:
            print(f"❌ Failed to install {package}")
            return False
    
    return True

def create_env_file():
    """Create a basic .env file"""
    if not os.path.exists('.env'):
        print("📄 Creating .env file...")
        with open('.env', 'w') as f:
            f.write("# Add your OpenAI API key here\n")
            f.write("OPENAI_API_KEY=your_api_key_here\n")
        print("✅ .env file created")
        print("⚠️ Please edit .env file with your OpenAI API key")

def start_app():
    """Start the Streamlit app"""
    print("🚀 Starting Ghostify Me...")
    
    try:
        # Start streamlit
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", "ghostify.py",
            "--server.port=8501"
        ])
    except KeyboardInterrupt:
        print("\n👻 Ghostify Me stopped")
    except Exception as e:
        print(f"❌ Error starting app: {e}")

def main():
    """Main quick start function"""
    print("👻 Ghostify Me - Quick Start")
    print("=" * 40)
    
    # Check if dependencies are installed
    try:
        import streamlit
        print("✅ Dependencies already installed")
    except ImportError:
        print("📦 Installing dependencies...")
        if not install_minimal_deps():
            print("❌ Installation failed")
            return
    
    # Create env file
    create_env_file()
    
    # Start the app
    start_app()

if __name__ == "__main__":
    main()
