# Use Python 3.11 slim image
FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    git \
    curl \
    wget \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first for better caching
COPY requirements.txt requirements-minimal.txt ./

# Install Python dependencies with fallback strategy
RUN pip install --upgrade pip && \
    pip install --no-cache-dir -r requirements-minimal.txt || \
    (echo "Minimal install failed, trying individual packages..." && \
     pip install streamlit requests openai fpdf2 Pillow) && \
    pip install --no-cache-dir -r requirements.txt || \
    echo "Some optional packages failed to install, continuing with core functionality"

# Copy application files
COPY . .

# Create necessary directories and set permissions
RUN mkdir -p /app/data /app/reports /app/logs && \
    chmod -R 755 /app/data /app/reports /app/logs

# Set environment variables
ENV PYTHONPATH=/app:/app/src
ENV STREAMLIT_SERVER_PORT=8501
ENV STREAMLIT_SERVER_ADDRESS=0.0.0.0
ENV STREAMLIT_SERVER_HEADLESS=true
ENV STREAMLIT_BROWSER_GATHER_USAGE_STATS=false

# Create non-root user for security
RUN useradd -m -u 1000 ghostify && \
    chown -R ghostify:ghostify /app
USER ghostify

# Expose port
EXPOSE 8501

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD curl -f http://localhost:8501/_stcore/health || exit 1

# Run the refactored application
CMD ["streamlit", "run", "ghostify_refactored.py", "--server.port=8501", "--server.address=0.0.0.0", "--server.headless=true"]
