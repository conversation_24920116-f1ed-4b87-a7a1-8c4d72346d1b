"""
Sherlock service implementation
Following Single Responsibility Principle and Dependency Inversion
"""

import os
import sys
import json
from pathlib import Path
from typing import List, Dict, Any

from .interfaces import ISherlockService
from ..core.models import Sherl<PERSON>R<PERSON><PERSON>, PlatformResult, AccountStatus, ScanConfiguration
from ..core.exceptions import SherlockScanError
from ..config.settings import get_config


class SherlockService(ISherlockService):
    """Service for Sherlock username enumeration"""
    
    def __init__(self):
        self.config = get_config().sherlock
        self.sherlock_path = self.config.get_sherlock_path()
    
    def scan_username(self, username: str, config: ScanConfiguration) -> SherlocResult:
        """Scan username across platforms using Sherlock"""
        if not self.is_available():
            return self._fallback_scan(username)
        
        try:
            return self._execute_sherlock_scan(username, config)
        except Exception as e:
            raise Sherlock<PERSON>canError(username, str(e), e)
    
    def is_available(self) -> bool:
        """Check if <PERSON> is available"""
        return (
            self.sherlock_path.exists() and
            (self.sherlock_path / "sherlock_project").exists() and
            (self.sherlock_path / "sherlock_project" / "resources" / "data.json").exists()
        )
    
    def _execute_sherlock_scan(self, username: str, config: ScanConfiguration) -> SherlocResult:
        """Execute actual Sherlock scan"""
        # Add sherlock to Python path
        if str(self.sherlock_path) not in sys.path:
            sys.path.insert(0, str(self.sherlock_path))
        
        try:
            # Import Sherlock modules
            from sherlock_project.sherlock import sherlock
            from sherlock_project.notify import QueryNotifyPrint
            from sherlock_project.result import QueryStatus
            
            # Load site data
            data_file = self.sherlock_path / "sherlock_project" / "resources" / "data.json"
            with open(data_file, 'r', encoding='utf-8') as f:
                site_data = json.load(f)
            
            # Create result collector
            class ResultCollector(QueryNotifyPrint):
                def __init__(self):
                    super().__init__()
                    self.results: List[PlatformResult] = []
                
                def update(self, result):
                    status = self._map_status(result.status)
                    platform_result = PlatformResult(
                        platform_name=result.site_name,
                        url=result.site_url_user,
                        status=status,
                        response_time=getattr(result, 'query_time', 0.0)
                    )
                    self.results.append(platform_result)
                
                def _map_status(self, sherlock_status) -> AccountStatus:
                    if sherlock_status == QueryStatus.CLAIMED:
                        return AccountStatus.FOUND
                    elif sherlock_status == QueryStatus.AVAILABLE:
                        return AccountStatus.NOT_FOUND
                    else:
                        return AccountStatus.UNKNOWN
            
            # Execute scan
            collector = ResultCollector()
            sherlock(
                username=username,
                site_data=site_data,
                query_notify=collector,
                timeout=config.scan_timeout,
                tor=self.config.use_tor,
                unique_tor=self.config.unique_tor,
                proxy=self.config.proxy
            )
            
            # Build result
            found_count = len([r for r in collector.results if r.status == AccountStatus.FOUND])
            
            return SherlocResult(
                username=username,
                platforms_checked=len(collector.results),
                platforms_found=found_count,
                results=collector.results
            )
            
        except ImportError as e:
            raise SherlockScanError(username, f"Sherlock import failed: {e}", e)
        except Exception as e:
            raise SherlockScanError(username, f"Sherlock execution failed: {e}", e)
    
    def _fallback_scan(self, username: str) -> SherlocResult:
        """Fallback scan when Sherlock is not available"""
        basic_platforms = {
            'GitHub': f'https://github.com/{username}',
            'Twitter': f'https://twitter.com/{username}',
            'Instagram': f'https://instagram.com/{username}',
            'Reddit': f'https://reddit.com/user/{username}',
            'LinkedIn': f'https://linkedin.com/in/{username}',
            'YouTube': f'https://youtube.com/c/{username}',
            'TikTok': f'https://tiktok.com/@{username}',
            'Pinterest': f'https://pinterest.com/{username}',
            'Snapchat': f'https://snapchat.com/add/{username}',
            'Telegram': f'https://t.me/{username}'
        }
        
        results = []
        for platform, url in basic_platforms.items():
            result = PlatformResult(
                platform_name=platform,
                url=url,
                status=AccountStatus.UNKNOWN,  # Requires manual verification
                response_time=0.0
            )
            results.append(result)
        
        return SherlocResult(
            username=username,
            platforms_checked=len(results),
            platforms_found=0,  # Unknown until manual verification
            results=results
        )


class MockSherlockService(ISherlockService):
    """Mock implementation for testing"""
    
    def scan_username(self, username: str, config: ScanConfiguration) -> SherlocResult:
        """Mock scan that returns sample data"""
        mock_results = [
            PlatformResult("GitHub", f"https://github.com/{username}", AccountStatus.FOUND),
            PlatformResult("Twitter", f"https://twitter.com/{username}", AccountStatus.NOT_FOUND),
            PlatformResult("Instagram", f"https://instagram.com/{username}", AccountStatus.UNKNOWN),
        ]
        
        return SherlocResult(
            username=username,
            platforms_checked=3,
            platforms_found=1,
            results=mock_results
        )
    
    def is_available(self) -> bool:
        """Mock is always available"""
        return True
