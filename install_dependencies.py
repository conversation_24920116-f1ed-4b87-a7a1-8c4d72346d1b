#!/usr/bin/env python3
"""
Smart dependency installer for Ghostify Me
Handles Windows compilation issues by installing packages individually
"""

import subprocess
import sys
import os

def install_package(package):
    """Install a single package with error handling"""
    print(f"📦 Installing {package}...")
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", package, "--upgrade"
        ])
        print(f"✅ {package} installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install {package}: {e}")
        return False

def install_with_precompiled(package):
    """Try to install precompiled version first"""
    print(f"📦 Installing {package} (trying precompiled)...")
    try:
        # Try with --only-binary to get precompiled wheels
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", package, 
            "--only-binary=all", "--upgrade"
        ])
        print(f"✅ {package} installed successfully (precompiled)")
        return True
    except subprocess.CalledProcessError:
        print(f"⚠️ Precompiled {package} not available, trying regular install...")
        return install_package(package)

def main():
    """Install dependencies intelligently"""
    print("👻 Ghostify Me - Smart Dependency Installer")
    print("=" * 50)
    
    # Core packages that usually work
    core_packages = [
        "streamlit",
        "requests", 
        "openai",
        "fpdf2",
        "Pillow"
    ]
    
    # Packages that might need precompiled versions
    optional_packages = [
        "pandas",
        "colorama",
        "PySocks",
        "requests-futures"
    ]
    
    # Sherlock-specific packages (most problematic)
    sherlock_packages = [
        "stem",
        "torrequest",
        "openpyxl",
        "certifi"
    ]
    
    print("🔧 Installing core packages...")
    core_success = 0
    for package in core_packages:
        if install_package(package):
            core_success += 1
    
    print(f"\n📊 Core packages: {core_success}/{len(core_packages)} installed")
    
    if core_success == len(core_packages):
        print("✅ All core packages installed! Ghostify Me should work.")
        
        print("\n🔧 Installing optional packages...")
        optional_success = 0
        for package in optional_packages:
            if install_with_precompiled(package):
                optional_success += 1
        
        print(f"\n📊 Optional packages: {optional_success}/{len(optional_packages)} installed")
        
        print("\n🔧 Installing Sherlock packages...")
        sherlock_success = 0
        for package in sherlock_packages:
            if install_package(package):
                sherlock_success += 1
        
        print(f"\n📊 Sherlock packages: {sherlock_success}/{len(sherlock_packages)} installed")
        
        print("\n🎉 Installation complete!")
        print(f"✅ Core: {core_success}/{len(core_packages)}")
        print(f"⚙️ Optional: {optional_success}/{len(optional_packages)}")
        print(f"🔍 Sherlock: {sherlock_success}/{len(sherlock_packages)}")
        
        if sherlock_success < len(sherlock_packages):
            print("\n⚠️ Some Sherlock features may be limited due to missing packages")
        
        print("\n🚀 Ready to run: python run_ghostify.py")
        
    else:
        print("❌ Core package installation failed. Please check your Python environment.")
        print("💡 Try running: python -m pip install --upgrade pip")

if __name__ == "__main__":
    main()
