#!/usr/bin/env python3
"""
Test the fix for the 'str' object has no attribute 'get' error
"""

import sys
import os

# Add current directory to path
sys.path.insert(0, os.getcwd())

def test_comprehensive_summary():
    """Test the comprehensive summary function with various data types"""
    print("🧪 Testing Comprehensive Summary Fix")
    print("=" * 40)
    
    try:
        from ghostify import generate_comprehensive_summary
        
        # Test with proper dictionaries
        print("✅ Testing with proper dictionaries...")
        sherlock_results = {
            'GitHub': {'status': 'found', 'url': 'https://github.com/testuser'},
            'Twitter': {'status': 'available', 'url': 'https://twitter.com/testuser'}
        }
        breach_data = {'breaches_found': 2, 'breach_names': ['LinkedIn', 'Adobe']}
        phone_analysis = {'formatted': '(*************', 'country_code': '+1'}
        image_info = {'status': 'uploaded', 'hash': 'abc123'}
        social_scan = {'platforms_checked': 10, 'search_urls': {'facebook': 'https://facebook.com'}}
        
        summary1 = generate_comprehensive_summary(
            "<PERSON>", "<EMAIL>", "testuser", "************",
            sherlock_results, breach_data, phone_analysis, image_info, social_scan
        )
        print("✅ Proper dictionaries test passed")
        
        # Test with empty dictionaries
        print("✅ Testing with empty dictionaries...")
        summary2 = generate_comprehensive_summary(
            "John Doe", "<EMAIL>", "testuser", "************",
            {}, {}, {}, {}, {}
        )
        print("✅ Empty dictionaries test passed")
        
        # Test with None values (this was causing the error)
        print("✅ Testing with None values...")
        summary3 = generate_comprehensive_summary(
            "John Doe", "<EMAIL>", "testuser", "************",
            None, None, None, None, None
        )
        print("✅ None values test passed")
        
        # Test with string values (this was causing the error)
        print("✅ Testing with string values...")
        summary4 = generate_comprehensive_summary(
            "John Doe", "<EMAIL>", "testuser", "************",
            "error", "error", "error", "error", "error"
        )
        print("✅ String values test passed")
        
        print("\n🎉 All comprehensive summary tests passed!")
        print("The 'str' object has no attribute 'get' error has been fixed!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_sherlock_fallback():
    """Test the Sherlock fallback functionality"""
    print("\n🔍 Testing Sherlock Fallback")
    print("=" * 40)
    
    try:
        from ghostify import run_sherlock_scan, run_basic_username_scan
        
        # Test basic username scan (fallback)
        print("✅ Testing basic username scan...")
        basic_results = run_basic_username_scan("testuser")
        print(f"   Basic scan returned {len(basic_results)} platforms")
        
        # Test Sherlock scan (will likely fall back to basic)
        print("✅ Testing Sherlock scan with fallback...")
        sherlock_results = run_sherlock_scan("testuser")
        print(f"   Sherlock scan returned {len(sherlock_results)} results")
        
        print("✅ Sherlock fallback tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Sherlock test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🔧 Testing Ghostify Fixes")
    print("=" * 50)
    
    test1_passed = test_comprehensive_summary()
    test2_passed = test_sherlock_fallback()
    
    if test1_passed and test2_passed:
        print("\n🎉 All tests passed! The fix is working correctly.")
        print("You can now run the Ghostify app without the 'get' attribute error.")
        return True
    else:
        print("\n❌ Some tests failed. Please check the output above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
