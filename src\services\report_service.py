"""
Report generation service implementation
"""

import json
from datetime import datetime
from typing import Dict, Any

from .interfaces import IReportService
from ..core.models import ScanResult
from ..core.exceptions import ReportGenerationError


class ReportService(IReportService):
    """Service for generating various report formats"""
    
    def generate_pdf_report(self, scan_result: ScanResult) -> bytes:
        """Generate PDF report"""
        try:
            from fpdf import FPDF
            
            pdf = FPDF()
            pdf.add_page()
            
            # Title
            pdf.set_font("Arial", 'B', 16)
            pdf.cell(200, 15, "GHOSTIFY ME - COMPREHENSIVE OSINT REPORT", ln=True, align='C')
            pdf.ln(5)
            
            # Target info
            pdf.set_font("Arial", 'B', 14)
            pdf.cell(200, 10, "TARGET PROFILE", ln=True)
            pdf.ln(5)
            
            pdf.set_font("Arial", size=11)
            target = scan_result.target
            pdf.cell(200, 8, f"Full Name: {target.full_name}", ln=True)
            pdf.cell(200, 8, f"Username: {target.username}", ln=True)
            pdf.cell(200, 8, f"Email: {target.email}", ln=True)
            pdf.cell(200, 8, f"Phone: {target.phone}", ln=True)
            pdf.cell(200, 8, f"Report Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}", ln=True)
            pdf.ln(10)
            
            # Results sections
            self._add_sherlock_section(pdf, scan_result)
            self._add_email_section(pdf, scan_result)
            self._add_threat_section(pdf, scan_result)
            
            # Footer
            pdf.ln(10)
            pdf.set_font("Arial", 'I', 10)
            pdf.cell(200, 10, "Generated by Ghostify Me - Digital Privacy Assessment Tool", ln=True, align='C')
            pdf.cell(200, 8, "For educational and privacy awareness purposes only", ln=True, align='C')
            
            return pdf.output(dest='S').encode('latin-1')
            
        except Exception as e:
            raise ReportGenerationError("PDF", str(e), e)
    
    def generate_text_report(self, scan_result: ScanResult) -> str:
        """Generate text report"""
        try:
            lines = []
            lines.append("GHOSTIFY ME - COMPREHENSIVE OSINT REPORT")
            lines.append("=" * 50)
            lines.append(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            lines.append("")
            
            # Target info
            target = scan_result.target
            lines.append("TARGET PROFILE:")
            lines.append(f"  Name: {target.full_name}")
            lines.append(f"  Username: {target.username}")
            lines.append(f"  Email: {target.email}")
            lines.append(f"  Phone: {target.phone}")
            lines.append("")
            
            # Sherlock results
            if scan_result.sherlock_result:
                sherlock = scan_result.sherlock_result
                lines.append("SHERLOCK RESULTS:")
                lines.append(f"  Platforms checked: {sherlock.platforms_checked}")
                lines.append(f"  Accounts found: {sherlock.platforms_found}")
                
                found_platforms = sherlock.get_found_platforms()
                if found_platforms:
                    lines.append("  Found accounts:")
                    for platform in found_platforms:
                        lines.append(f"    - {platform.platform_name}: {platform.url}")
                lines.append("")
            
            # Email analysis
            if scan_result.email_analysis:
                email = scan_result.email_analysis
                lines.append("EMAIL ANALYSIS:")
                lines.append(f"  Breaches found: {email.breaches_found}")
                if email.breach_details:
                    lines.append("  Breach details:")
                    for breach in email.breach_details:
                        lines.append(f"    - {breach.breach_name}")
                lines.append("")
            
            # Threat assessment
            if scan_result.threat_assessment:
                threat = scan_result.threat_assessment
                lines.append("THREAT ASSESSMENT:")
                lines.append(f"  Threat level: {threat.threat_level.value}")
                if threat.vulnerabilities:
                    lines.append("  Vulnerabilities:")
                    for vuln in threat.vulnerabilities:
                        lines.append(f"    - {vuln}")
                if threat.immediate_actions:
                    lines.append("  Immediate actions:")
                    for action in threat.immediate_actions:
                        lines.append(f"    - {action}")
                lines.append("")
            
            lines.append("=" * 50)
            lines.append("For educational and privacy awareness purposes only")
            
            return "\n".join(lines)
            
        except Exception as e:
            raise ReportGenerationError("text", str(e), e)
    
    def generate_json_report(self, scan_result: ScanResult) -> Dict[str, Any]:
        """Generate JSON report"""
        try:
            report = {
                "metadata": {
                    "generated_at": datetime.now().isoformat(),
                    "tool": "Ghostify Me",
                    "version": "2.0.0"
                },
                "target": {
                    "full_name": scan_result.target.full_name,
                    "username": scan_result.target.username,
                    "email": scan_result.target.email,
                    "phone": scan_result.target.phone
                },
                "scan_info": {
                    "status": scan_result.status.value,
                    "started_at": scan_result.started_at.isoformat() if scan_result.started_at else None,
                    "completed_at": scan_result.completed_at.isoformat() if scan_result.completed_at else None,
                    "duration": scan_result.get_duration()
                },
                "results": {}
            }
            
            # Add results sections
            if scan_result.sherlock_result:
                report["results"]["sherlock"] = self._sherlock_to_dict(scan_result.sherlock_result)
            
            if scan_result.email_analysis:
                report["results"]["email"] = self._email_to_dict(scan_result.email_analysis)
            
            if scan_result.phone_analysis:
                report["results"]["phone"] = self._phone_to_dict(scan_result.phone_analysis)
            
            if scan_result.threat_assessment:
                report["results"]["threat_assessment"] = self._threat_to_dict(scan_result.threat_assessment)
            
            return report
            
        except Exception as e:
            raise ReportGenerationError("JSON", str(e), e)
    
    def _add_sherlock_section(self, pdf, scan_result: ScanResult):
        """Add Sherlock section to PDF"""
        if not scan_result.sherlock_result:
            return
        
        sherlock = scan_result.sherlock_result
        pdf.set_font("Arial", 'B', 14)
        pdf.cell(200, 10, "SHERLOCK RESULTS", ln=True)
        pdf.ln(5)
        
        pdf.set_font("Arial", size=11)
        pdf.cell(200, 8, f"Platforms checked: {sherlock.platforms_checked}", ln=True)
        pdf.cell(200, 8, f"Accounts found: {sherlock.platforms_found}", ln=True)
        
        found_platforms = sherlock.get_found_platforms()
        if found_platforms:
            pdf.cell(200, 8, "Found accounts:", ln=True)
            for platform in found_platforms[:10]:  # Limit to 10 for space
                pdf.cell(200, 6, f"  - {platform.platform_name}", ln=True)
        
        pdf.ln(5)
    
    def _add_email_section(self, pdf, scan_result: ScanResult):
        """Add email section to PDF"""
        if not scan_result.email_analysis:
            return
        
        email = scan_result.email_analysis
        pdf.set_font("Arial", 'B', 14)
        pdf.cell(200, 10, "EMAIL ANALYSIS", ln=True)
        pdf.ln(5)
        
        pdf.set_font("Arial", size=11)
        pdf.cell(200, 8, f"Breaches found: {email.breaches_found}", ln=True)
        
        if email.breach_details:
            pdf.cell(200, 8, "Breach details:", ln=True)
            for breach in email.breach_details[:5]:  # Limit to 5
                pdf.cell(200, 6, f"  - {breach.breach_name}", ln=True)
        
        pdf.ln(5)
    
    def _add_threat_section(self, pdf, scan_result: ScanResult):
        """Add threat assessment section to PDF"""
        if not scan_result.threat_assessment:
            return
        
        threat = scan_result.threat_assessment
        pdf.set_font("Arial", 'B', 14)
        pdf.cell(200, 10, "THREAT ASSESSMENT", ln=True)
        pdf.ln(5)
        
        pdf.set_font("Arial", size=11)
        pdf.cell(200, 8, f"Threat level: {threat.threat_level.value}", ln=True)
        
        if threat.immediate_actions:
            pdf.cell(200, 8, "Immediate actions:", ln=True)
            for action in threat.immediate_actions[:5]:
                pdf.cell(200, 6, f"  - {action[:60]}...", ln=True)  # Truncate long lines
        
        pdf.ln(5)
    
    def _sherlock_to_dict(self, sherlock_result) -> Dict[str, Any]:
        """Convert Sherlock result to dictionary"""
        return {
            "username": sherlock_result.username,
            "platforms_checked": sherlock_result.platforms_checked,
            "platforms_found": sherlock_result.platforms_found,
            "found_accounts": [
                {
                    "platform": p.platform_name,
                    "url": p.url,
                    "status": p.status.value
                }
                for p in sherlock_result.get_found_platforms()
            ]
        }
    
    def _email_to_dict(self, email_analysis) -> Dict[str, Any]:
        """Convert email analysis to dictionary"""
        return {
            "email": email_analysis.email,
            "breaches_found": email_analysis.breaches_found,
            "is_compromised": email_analysis.is_compromised(),
            "reputation_score": email_analysis.reputation_score,
            "breach_details": [
                {
                    "name": b.breach_name,
                    "date": b.breach_date.isoformat() if b.breach_date else None,
                    "compromised_data": b.compromised_data
                }
                for b in email_analysis.breach_details
            ]
        }
    
    def _phone_to_dict(self, phone_analysis) -> Dict[str, Any]:
        """Convert phone analysis to dictionary"""
        return {
            "original": phone_analysis.original_number,
            "formatted": phone_analysis.formatted_number,
            "country_code": phone_analysis.country_code,
            "carrier": phone_analysis.carrier,
            "line_type": phone_analysis.line_type,
            "is_valid": phone_analysis.is_valid
        }
    
    def _threat_to_dict(self, threat_assessment) -> Dict[str, Any]:
        """Convert threat assessment to dictionary"""
        return {
            "threat_level": threat_assessment.threat_level.value,
            "vulnerabilities": threat_assessment.vulnerabilities,
            "immediate_actions": threat_assessment.immediate_actions,
            "long_term_recommendations": threat_assessment.long_term_recommendations,
            "confidence_score": threat_assessment.confidence_score
        }
