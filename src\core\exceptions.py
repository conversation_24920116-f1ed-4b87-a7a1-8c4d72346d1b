"""
Custom exceptions for Ghostify Me OSINT Scanner
Following clean error handling principles
"""

from typing import Optional, Dict, Any


class GhostifyError(Exception):
    """Base exception for all Ghostify errors"""
    
    def __init__(self, message: str, error_code: Optional[str] = None, details: Optional[Dict[str, Any]] = None):
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.details = details or {}


class ValidationError(GhostifyError):
    """Raised when input validation fails"""
    
    def __init__(self, field: str, value: Any, message: str):
        super().__init__(f"Validation failed for {field}: {message}", "VALIDATION_ERROR")
        self.field = field
        self.value = value


class ConfigurationError(GhostifyError):
    """Raised when configuration is invalid or missing"""
    
    def __init__(self, setting: str, message: str):
        super().__init__(f"Configuration error for {setting}: {message}", "CONFIG_ERROR")
        self.setting = setting


class ScanError(GhostifyError):
    """Base class for scan-related errors"""
    pass


class SherlockScanError(ScanError):
    """Raised when Sherlock scan fails"""
    
    def __init__(self, username: str, message: str, original_error: Optional[Exception] = None):
        super().__init__(f"Sherlock scan failed for {username}: {message}", "SHERLOCK_ERROR")
        self.username = username
        self.original_error = original_error


class EmailAnalysisError(ScanError):
    """Raised when email analysis fails"""
    
    def __init__(self, email: str, message: str, original_error: Optional[Exception] = None):
        super().__init__(f"Email analysis failed for {email}: {message}", "EMAIL_ERROR")
        self.email = email
        self.original_error = original_error


class PhoneAnalysisError(ScanError):
    """Raised when phone analysis fails"""
    
    def __init__(self, phone: str, message: str, original_error: Optional[Exception] = None):
        super().__init__(f"Phone analysis failed for {phone}: {message}", "PHONE_ERROR")
        self.phone = phone
        self.original_error = original_error


class ImageAnalysisError(ScanError):
    """Raised when image analysis fails"""
    
    def __init__(self, message: str, original_error: Optional[Exception] = None):
        super().__init__(f"Image analysis failed: {message}", "IMAGE_ERROR")
        self.original_error = original_error


class AIAnalysisError(ScanError):
    """Raised when AI analysis fails"""
    
    def __init__(self, message: str, original_error: Optional[Exception] = None):
        super().__init__(f"AI analysis failed: {message}", "AI_ERROR")
        self.original_error = original_error


class DatabaseError(GhostifyError):
    """Raised when database operations fail"""
    
    def __init__(self, operation: str, message: str, original_error: Optional[Exception] = None):
        super().__init__(f"Database {operation} failed: {message}", "DATABASE_ERROR")
        self.operation = operation
        self.original_error = original_error


class ExternalServiceError(GhostifyError):
    """Raised when external service calls fail"""
    
    def __init__(self, service: str, message: str, status_code: Optional[int] = None):
        super().__init__(f"External service {service} failed: {message}", "EXTERNAL_SERVICE_ERROR")
        self.service = service
        self.status_code = status_code


class ReportGenerationError(GhostifyError):
    """Raised when report generation fails"""
    
    def __init__(self, report_type: str, message: str, original_error: Optional[Exception] = None):
        super().__init__(f"Report generation failed for {report_type}: {message}", "REPORT_ERROR")
        self.report_type = report_type
        self.original_error = original_error


class RateLimitError(ExternalServiceError):
    """Raised when rate limits are exceeded"""
    
    def __init__(self, service: str, retry_after: Optional[int] = None):
        message = f"Rate limit exceeded"
        if retry_after:
            message += f", retry after {retry_after} seconds"
        super().__init__(service, message)
        self.retry_after = retry_after


class AuthenticationError(ExternalServiceError):
    """Raised when authentication fails"""
    
    def __init__(self, service: str, message: str = "Authentication failed"):
        super().__init__(service, message, 401)


class PermissionError(ExternalServiceError):
    """Raised when permission is denied"""
    
    def __init__(self, service: str, message: str = "Permission denied"):
        super().__init__(service, message, 403)
